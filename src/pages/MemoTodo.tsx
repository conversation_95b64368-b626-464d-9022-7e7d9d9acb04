import React, { useState, useEffect, useRef, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { useAppContext } from "@/context/AppContext";
import { useMemos, Memo } from "@/hooks/useMemos";
import { useTodos, Todo } from "@/hooks/useTodos";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { motion, AnimatePresence } from "framer-motion";
import { format, isBefore, isToday, isTomorrow, isAfter, parseISO, isValid } from "date-fns";
import { zhCN, enUS } from "date-fns/locale";
import { cn } from "@/lib/utils";
import SearchBar from "./memo-todo/SearchBar";
import SidebarNav from "./memo-todo/SidebarNav";
import TodoSection from "./memo-todo/TodoSection";
import MemoSection from "./memo-todo/MemoSection";
import MemoDialog from "./memo-todo/MemoDialog";
import MemoDetailDialog from "./memo-todo/MemoDetailDialog";
import { Edit2, Trash2, ListTodo } from 'lucide-react'; // Import icons for detail view
import { MarkdownMemo } from '@/components/MarkdownMemo'; // Ensure MarkdownMemo is imported for memo detail view
import { Badge } from '@/components/ui/badge'; // Ensure Badge is imported for detail view
import TodoEditDialog from "./memo-todo/TodoEditDialog";
import CompactPageLayout from '@/components/layout/CompactPageLayout';
import { AuthModal } from '@/components/auth/AuthModal';
import { useI18n } from '@/hooks/useI18n';

const MemoTodo = () => {
  const { user, isAuthReady } = useAppContext();
  const { t, language, dateLocale } = useI18n();
  const navigate = useNavigate();
  const { toast } = useToast();

  // Data state and hooks
  const { memos, loading: memosLoading, addMemo, updateMemo, deleteMemo, refreshMemos } = useMemos();
  const { todos, loading: todosLoading, addTodo, toggleTodo, updateTodo, deleteTodo, refreshTodos } = useTodos();

  // UI state
  const [newMemo, setNewMemo] = useState('');
  const [newTodo, setNewTodo] = useState('');
  const [searchText, setSearchText] = useState('');
  const [memoDialogOpen, setMemoDialogOpen] = useState(false);
  const [detailDialogOpen, setDetailDialogOpen] = useState(false);
  const [currentMemo, setCurrentMemo] = useState<Memo | null>(null);
  const [completedVisibility, setCompletedVisibility] = useState('active'); // Default filter for active
  const [activeCategory, setActiveCategory] = useState('all');
  const [dueDateFilter, setDueDateFilter] = useState<string>('upcoming'); // Default filter for upcoming
  const memoInputRef = useRef<HTMLTextAreaElement>(null);
  const todoInputRef = useRef<HTMLInputElement>(null);

  const [selectedDueDate, setSelectedDueDate] = useState<Date | undefined>(undefined);
  const [selectedReminderDate, setSelectedReminderDate] = useState<Date | undefined>(undefined);
  const [selectedPriority, setSelectedPriority] = useState<'low' | 'medium' | 'high' | undefined>(undefined);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState('');

  const [isEditMode, setIsEditMode] = useState(false); // This might be reused or adapted for the detail view's edit functionality

  const [todoEditDialogOpen, setTodoEditDialogOpen] = useState(false);
  const [currentTodo, setCurrentTodo] = useState<Todo | null>(null);
  const [currentView, setCurrentView] = useState<'list' | 'memoDetail' | 'todoDetail'>('list'); // 'list', 'memoDetail', 'todoDetail'
  const [selectedItem, setSelectedItem] = useState<Memo | Todo | null>(null);
  const [activeViewType, setActiveViewType] = useState<'todos' | 'memos'>('todos'); // Track which section is active

  // Pagination state for memos
  const [currentMemoPage, setCurrentMemoPage] = useState(1);
  const memosPerPage = 6; // Display 6 memos per page, adjust as needed

  // Auth modal state
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [authMode, setAuthMode] = useState<'login' | 'register'>('login');

  // Auth modal handler functions
  const handleLogin = () => {
    setAuthMode('login');
    setIsAuthModalOpen(true);
  };

  const handleAuthModalClose = () => {
    setIsAuthModalOpen(false);
  };

  // Refresh data when auth state changes
  useEffect(() => {
    // Only fetch data when user is logged in
    if (user) {
      refreshMemos();
      refreshTodos();
    }
  }, [user, refreshMemos, refreshTodos]);

  // Filter todos - use useMemo to reduce unnecessary recalculations
  const filteredTodos = useMemo(() => {
    // Ensure todos is an array and not empty
    if (!Array.isArray(todos) || todos.length === 0) {
      return [];
    }

    return todos.filter(todo => {
      // Enhanced safety check: ensure todo object and its necessary properties exist
      if (!todo || typeof todo !== 'object' || !todo.id) {
        console.warn('Invalid todo object:', todo);
        return false;
      }

      // Ensure title exists and is a string
      if (!todo.title || typeof todo.title !== 'string' || todo.title.trim() === '') {
        console.warn('Todo missing valid title:', todo);
        return false;
      }

      try {
        // Text search filter - add additional safety checks
        const safeSearchText = searchText || '';
        const matchesSearch = todo.title.toLowerCase().includes(safeSearchText.toLowerCase());

        // Completion status filter
        const matchesCompletion =
          completedVisibility === 'all' ||
          (completedVisibility === 'active' && !todo.completed) ||
          (completedVisibility === 'completed' && todo.completed);

        // Date filter
        let matchesDueDate = true;

        try {
          if (dueDateFilter !== 'all') {
            if (dueDateFilter === 'no-date') {
              matchesDueDate = !todo.due_date;
            } else if (todo.due_date) {
              const dueDate = parseISO(todo.due_date);
              if (isValid(dueDate)) {
                const now = new Date();
                if (dueDateFilter === 'today') {
                  matchesDueDate = isToday(dueDate);
                } else if (dueDateFilter === 'tomorrow') {
                  matchesDueDate = isTomorrow(dueDate);
                } else if (dueDateFilter === 'upcoming') {
                  // Upcoming: future dates, but not today and tomorrow
                  matchesDueDate = isAfter(dueDate, now) && !isToday(dueDate) && !isTomorrow(dueDate);
                } else if (dueDateFilter === 'overdue') {
                  // Overdue: past dates and not completed
                  matchesDueDate = isBefore(dueDate, now) && !isToday(dueDate) && !todo.completed;
                } else {
                  matchesDueDate = true;
                }

                // Debug info
                if (process.env.NODE_ENV === 'development') {
                  console.log(`Todo ${todo.id}: ${todo.title}, due: ${todo.due_date}, filter: ${dueDateFilter}, matches: ${matchesDueDate}`);
                }
              } else {
                matchesDueDate = dueDateFilter === 'no-date';
              }
            } else {
              matchesDueDate = dueDateFilter === 'no-date';
            }
          }
        } catch (dateError) {
          console.warn('Date parsing error for todo:', todo.id, dateError);
          matchesDueDate = true;
        }

        return matchesSearch && matchesCompletion && matchesDueDate;
      } catch (error) {
        console.error('Error filtering todo:', todo.id, error);
        return false;
      }
    });
  }, [todos, searchText, completedVisibility, dueDateFilter]);

  // Filter memos - use useMemo to reduce unnecessary recalculations
  const filteredMemos = useMemo(() => {
    // Ensure memos is an array and not empty
    if (!Array.isArray(memos) || memos.length === 0) {
      console.log('MemoTodo: filteredMemos - memos array is empty or invalid:', memos);
      return [];
    }

    console.log('MemoTodo: filteredMemos - processing memos:', memos.length);

    const filtered = memos.filter(memo => {
      // Enhanced safety check: ensure memo object and its necessary properties exist
      const memoId = memo.id || memo.ID;
      const memoContent = memo.content || memo.Content;
      
      if (!memo || typeof memo !== 'object' || !memoId) {
        console.log('MemoTodo: filteredMemos - Invalid memo object:', memo);
        return false;
      }

      // Ensure content exists and is a string
      if (!memoContent || typeof memoContent !== 'string' || memoContent.trim() === '') {
        console.log('MemoTodo: filteredMemos - Memo missing valid content:', memo);
        return false;
      }

      try {
        // Text search filter - add additional safety checks
        const safeSearchText = searchText || '';
        const matchesSearch = memoContent.toLowerCase().includes(safeSearchText.toLowerCase());

        // Tag filter (if activeCategory is a tag)
        let matchesCategory = true;
        const memoTags = memo.tags || memo.Tags || [];

        if (activeCategory !== 'all') {
          if (!Array.isArray(memoTags) || memoTags.length === 0) {
            matchesCategory = false;
          } else {
            matchesCategory = memoTags.some(
              tag => tag && typeof tag === 'string' && tag.trim() !== '' && tag.toLowerCase() === activeCategory.toLowerCase()
            );
          }
        }

        // Date filter for memos (based on reminder_date)
        let matchesReminderDate = true;
        try {
          if (dueDateFilter !== 'all') {
            if (dueDateFilter === 'no-date') {
              matchesReminderDate = !memo.reminder_date;
            } else if (memo.reminder_date) {
              const reminderDate = parseISO(memo.reminder_date);
              if (isValid(reminderDate)) {
                const now = new Date();
                if (dueDateFilter === 'today') {
                  matchesReminderDate = isToday(reminderDate);
                } else if (dueDateFilter === 'tomorrow') {
                  matchesReminderDate = isTomorrow(reminderDate);
                } else if (dueDateFilter === 'upcoming') {
                  // Upcoming: future dates, but not today and tomorrow
                  matchesReminderDate = isAfter(reminderDate, now) && !isToday(reminderDate) && !isTomorrow(reminderDate);
                } else if (dueDateFilter === 'overdue') {
                  // Overdue: past dates
                  matchesReminderDate = isBefore(reminderDate, now) && !isToday(reminderDate);
                } else {
                  matchesReminderDate = true;
                }
              } else {
                matchesReminderDate = dueDateFilter === 'no-date';
              }
            } else {
              matchesReminderDate = dueDateFilter === 'no-date';
            }
          }
        } catch (dateError) {
          console.warn('Date parsing error for memo:', memoId, dateError);
          matchesReminderDate = true;
        }

        const result = matchesSearch && matchesCategory && matchesReminderDate;
        console.log(`MemoTodo: filteredMemos - Memo ${memoId}: search=${matchesSearch}, category=${matchesCategory}, result=${result}`);
        return result;
      } catch (error) {
        console.error('MemoTodo: filteredMemos - Error filtering memo:', memoId, error);
        return false;
      }
    });

    console.log('MemoTodo: filteredMemos - Final filtered count:', filtered.length);
    return filtered;
  }, [memos, searchText, activeCategory, dueDateFilter]);

  // Get all available tag categories
  const allTags = useMemo(() => {
    const tagSet = new Set<string>();

    // Ensure memos is an array and not empty
    if (!Array.isArray(memos) || memos.length === 0) {
      return [];
    }

    try {
      memos.forEach(memo => {
        // Enhanced safety check with both field formats
        const memoTags = memo.tags || memo.Tags || [];
        if (memo && typeof memo === 'object' && Array.isArray(memoTags) && memoTags.length > 0) {
          memoTags.forEach(tag => {
            if (tag && typeof tag === 'string' && tag.trim() !== '') {
              tagSet.add(tag.toLowerCase());
            }
          });
        }
      });
    } catch (error) {
      console.error('Error processing tags:', error);
    }

    return Array.from(tagSet);
  }, [memos]);

  const handleAddMemo = async (e: React.FormEvent) => {
    e.preventDefault();
    if (newMemo.trim()) {
      try {
        if (currentMemo && isEditMode) {
          await updateMemo(currentMemo.id, {
            content: newMemo.trim(),
            reminder_date: selectedReminderDate ? selectedReminderDate.toISOString() : null,
            tags: selectedTags.length > 0 ? selectedTags : null
          });
        } else {
          await addMemo(
            newMemo.trim(),
            selectedReminderDate ? selectedReminderDate.toISOString() : undefined,
            selectedTags.length > 0 ? selectedTags : undefined
          );
        }
        resetMemoForm();
        // 主动刷新数据
        await refreshMemos();
      } catch (error) {
        console.error('Failed to add/update memo:', error);
      }
    }
  };

  const resetMemoForm = () => {
    setNewMemo('');
    setSelectedReminderDate(undefined);
    setSelectedTags([]);
    setMemoDialogOpen(false);
    setIsEditMode(false);
    setCurrentMemo(null);
  };

  const handleAddTodo = (e: React.FormEvent) => {
    e.preventDefault();
    if (newTodo.trim()) {
      addTodo(
        newTodo.trim(),
        selectedDueDate ? selectedDueDate.toISOString() : undefined,
        selectedPriority
      );
      setNewTodo('');
      setSelectedDueDate(undefined);
      setSelectedPriority(undefined);
      todoInputRef.current?.focus();
    }
  };

  const handleEditTodo = () => {
    if (currentTodo && newTodo.trim()) {
      updateTodo(currentTodo.id, {
        title: newTodo.trim(),
        due_date: selectedDueDate ? selectedDueDate.toISOString() : null,
        priority: selectedPriority
      });
      setNewTodo('');
      setSelectedDueDate(undefined);
      setSelectedPriority(undefined);
      setTodoEditDialogOpen(false);
      setCurrentTodo(null);
    }
  };

  const handleOpenTodoEdit = (todo: Todo) => {
    setCurrentTodo(todo);
    setNewTodo(todo.title);
    setSelectedDueDate(todo.due_date ? parseISO(todo.due_date) : undefined);
    setSelectedPriority(todo.priority || undefined);
    setTodoEditDialogOpen(true);
  };

  const handleViewMemoDetails = (memo: Memo) => {
    setCurrentMemo(memo);
    setDetailDialogOpen(true);
  };

  const handleShowMemoDetails = (memo: Memo) => {
    setSelectedItem(memo);
    setCurrentView('memoDetail');
  };

  const handleShowTodoDetails = (todo: Todo) => {
    setSelectedItem(todo);
    setCurrentView('todoDetail');
  };

  const handleBackToList = () => {
    setCurrentView('list');
    setSelectedItem(null);
  };

  const handleEditMemo = (memo: Memo) => {
    setCurrentMemo(memo);
    const memoContent = memo.content || memo.Content;
    const memoReminderDate = memo.reminder_date || memo.ReminderDate;
    const memoTags = memo.tags || memo.Tags;
    
    setNewMemo(memoContent);
    setSelectedReminderDate(memoReminderDate ? parseISO(memoReminderDate) : undefined);
    // Safely handle tags array
    const safeTags = memoTags && Array.isArray(memoTags) 
      ? memoTags.filter(tag => tag && typeof tag === 'string').map(tag => tag.toString())
      : [];
    setSelectedTags(safeTags);
    setIsEditMode(true);
    setMemoDialogOpen(true);
  };

  const addTag = () => {
    if (newTag.trim() && !selectedTags.includes(newTag.trim())) {
      setSelectedTags([...selectedTags, newTag.trim()]);
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setSelectedTags(selectedTags.filter(tag => tag !== tagToRemove));
  };

  // Show login prompt for unauthenticated users
  if (!isAuthReady) {
    return (
      <div className="flex justify-center items-center min-h-[calc(100vh-4rem)]">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex justify-center items-center min-h-[calc(100vh-4rem)]">
        <Card className="w-full max-w-md p-6">
          <CardContent className="text-center space-y-4">
            <div className="text-4xl text-muted-foreground">🔒</div>
            <p className="text-lg font-medium">
              {t('todoMemo.loginRequired')}
            </p>
            <Button onClick={handleLogin}>
              {t('todoMemo.login')}
            </Button>
          </CardContent>
        </Card>

        {/* Auth modal */}
        <AuthModal
          isOpen={isAuthModalOpen}
          onClose={handleAuthModalClose}
          defaultMode={authMode}
        />
      </div>
    );
  }

  if (memosLoading || todosLoading) {
    return (
      <div className="flex justify-center items-center min-h-[calc(100vh-4rem)] flex-col gap-4">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        <p className="text-muted-foreground">
          {t('todoMemo.loading')}
        </p>
      </div>
    );
  }

  return (
    <CompactPageLayout
      title={t('navigation.memoAndTodo')}
      description={t('todoMemo.description')}
      icon={ListTodo}
    >
      <div className="max-w-7xl mx-auto px-4 pt-1 md:pt-2 space-y-4">

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <div className="sticky top-20">
            <SearchBar
              searchText={searchText}
              setSearchText={setSearchText}
            />
            <div className="mt-4">
              <SidebarNav
                todos={todos}
                filteredTodos={filteredTodos}
                memos={memos}
                activeCategory={activeCategory}
                setActiveCategory={setActiveCategory}
                completedVisibility={completedVisibility}
                setCompletedVisibility={setCompletedVisibility}
                dueDateFilter={dueDateFilter}
                setDueDateFilter={setDueDateFilter}
                availableTags={allTags}
                activeView={activeViewType}
              />
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3 space-y-4">
          {currentView === 'list' ? (
            <>
              <TodoSection
                newTodo={newTodo}
                setNewTodo={setNewTodo}
                todoInputRef={todoInputRef}
                selectedDueDate={selectedDueDate}
                setSelectedDueDate={setSelectedDueDate}
                selectedPriority={selectedPriority}
                setSelectedPriority={setSelectedPriority}
                handleAddTodo={handleAddTodo}
                todos={filteredTodos}
                toggleTodo={(id: string) => toggleTodo(id, !todos.find(t => t.id === id)?.completed)}
                handleOpenTodoEdit={handleOpenTodoEdit}
                deleteTodo={deleteTodo}
                handleViewTodoDetails={handleShowTodoDetails}
                todosLoading={todosLoading}
                onFocus={() => setActiveViewType('todos')}
              />

              <MemoSection
                searchText={searchText}
                handleViewMemoDetails={handleShowMemoDetails} // Use the new handler for detail view
                handleEditMemo={handleEditMemo} // Keep for edit dialog functionality
                deleteMemo={deleteMemo}
                memos={filteredMemos}
                setMemoDialogOpen={setMemoDialogOpen}
                setIsEditMode={setIsEditMode}
                setNewMemo={setNewMemo}
                setSelectedReminderDate={setSelectedReminderDate}
                setSelectedTags={setSelectedTags}
                currentPage={currentMemoPage} // Pass current page
                itemsPerPage={memosPerPage} // Pass items per page
                onPageChange={setCurrentMemoPage} // Pass page change handler
                onFocus={() => setActiveViewType('memos')}
              />
            </>
          ) : currentView === 'memoDetail' && selectedItem && 'content' in selectedItem ? (
            <Card>
              <CardContent className="p-4">
                <Button onClick={handleBackToList} variant="outline" className="mb-4">
                  {t('todoMemo.backToList')}
                </Button>
                <h2 className="text-2xl font-semibold mb-3">
                  {(selectedItem as Memo).content && (selectedItem as Memo).content.length > 0
                    ? (selectedItem as Memo).content.substring(0, 50) + ((selectedItem as Memo).content.length > 50 ? '...' : '')
                    : 'Untitled'
                  }
                </h2>
                <div className="prose prose-sm max-w-none dark:prose-invert mb-4">
                  <MarkdownMemo content={(selectedItem as Memo).content} />
                </div>
                {(selectedItem as Memo).tags && Array.isArray((selectedItem as Memo).tags) && ((selectedItem as Memo).tags?.length ?? 0) > 0 && (
                  <div className="mb-2">
                    <strong>{t('todoMemo.tags')}</strong>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {(selectedItem as Memo).tags!
                        .filter(tag => tag && typeof tag === 'string')
                        .map((tag, index) => (
                          <Badge key={index} variant="secondary">{tag}</Badge>
                        ))}
                    </div>
                  </div>
                )}
                {(selectedItem as Memo).reminder_date && (
                  <p className="text-sm text-muted-foreground mb-1">
                    <strong>{t('todoMemo.reminder')}</strong> {format(parseISO((selectedItem as Memo).reminder_date!), 'PPP p', { locale: dateLocale })}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mb-4">
                  <strong>{t('todoMemo.created')}</strong> {format(parseISO((selectedItem as Memo).created_at), 'PPP p', { locale: dateLocale })}
                </p>
                <div className="flex gap-2">
                  <Button onClick={() => handleEditMemo(selectedItem as Memo)} size="sm">
                    <Edit2 className="h-4 w-4 mr-2" />
                    {t('todoMemo.edit')}
                  </Button>
                  <Button onClick={() => { deleteMemo((selectedItem as Memo).id); handleBackToList(); }} variant="destructive" size="sm">
                    <Trash2 className="h-4 w-4 mr-2" />
                    {t('todoMemo.delete')}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : currentView === 'todoDetail' && selectedItem && 'title' in selectedItem ? (
            <Card>
              <CardContent className="p-4">
                <Button onClick={handleBackToList} variant="outline" className="mb-4">
                  {t('todoMemo.backToList')}
                </Button>
                <h2 className="text-2xl font-semibold mb-3">{(selectedItem as Todo).title}</h2>
                <p className={cn("mb-2", (selectedItem as Todo).completed ? "text-green-600" : "text-amber-600")}>
                  <strong>{t('todoMemo.status')}</strong>
                  {(selectedItem as Todo).completed ? t('todoMemo.completed') : t('todoMemo.pending')}
                </p>
                {(selectedItem as Todo).due_date && (
                  <p className="text-sm text-muted-foreground mb-1">
                    <strong>{t('todoMemo.dueDate')}</strong> {format(parseISO((selectedItem as Todo).due_date!), 'PPP', { locale: dateLocale })}
                  </p>
                )}
                {(selectedItem as Todo).priority && (
                  <p className="text-sm text-muted-foreground mb-1">
                    <strong>{t('todoMemo.priority')}</strong>
                    <Badge variant={(selectedItem as Todo).priority === 'high' ? 'destructive' : (selectedItem as Todo).priority === 'medium' ? 'secondary' : 'outline'} className="ml-1">
                      {(selectedItem as Todo).priority === 'high'
                        ? t('todoMemo.high')
                        : (selectedItem as Todo).priority === 'medium'
                          ? t('todoMemo.medium')
                          : t('todoMemo.low')}
                    </Badge>
                  </p>
                )}
                 <p className="text-sm text-muted-foreground mb-4">
                  <strong>{t('todoMemo.created')}</strong> {format(parseISO((selectedItem as Todo).created_at), 'PPP p', { locale: dateLocale })}
                </p>
                <div className="flex gap-2">
                  <Button onClick={() => handleOpenTodoEdit(selectedItem as Todo)} size="sm">
                    <Edit2 className="h-4 w-4 mr-2" />
                    {t('todoMemo.edit')}
                  </Button>
                  <Button onClick={() => { deleteTodo((selectedItem as Todo).id); handleBackToList(); }} variant="destructive" size="sm">
                    <Trash2 className="h-4 w-4 mr-2" />
                    {t('todoMemo.delete')}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : null}
        </div>
      </div>

      <MemoDialog
        memoDialogOpen={memoDialogOpen}
        setMemoDialogOpen={setMemoDialogOpen}
        isEditMode={isEditMode}
        newMemo={newMemo}
        setNewMemo={setNewMemo}
        selectedReminderDate={selectedReminderDate}
        setSelectedReminderDate={setSelectedReminderDate}
        selectedTags={selectedTags}
        setSelectedTags={setSelectedTags}
        newTag={newTag}
        setNewTag={setNewTag}
        handleAddMemo={handleAddMemo}
        addTag={addTag}
        removeTag={removeTag}
      />
      <MemoDetailDialog
        open={detailDialogOpen}
        onOpenChange={setDetailDialogOpen}
        memo={currentMemo}
        deleteMemo={deleteMemo}
        handleEditMemo={handleEditMemo}
      />
      <TodoEditDialog
        open={todoEditDialogOpen}
        onOpenChange={setTodoEditDialogOpen}
        newTodo={newTodo}
        setNewTodo={setNewTodo}
        selectedDueDate={selectedDueDate}
        setSelectedDueDate={setSelectedDueDate}
        selectedPriority={selectedPriority}
        setSelectedPriority={setSelectedPriority}
        handleEditTodo={handleEditTodo}
      />
    </div>
    </CompactPageLayout>
  );
};

export default MemoTodo;
