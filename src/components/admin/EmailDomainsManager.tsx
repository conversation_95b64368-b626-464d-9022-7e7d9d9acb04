import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Trash2, Plus, Mail } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useTranslation } from 'react-i18next';
import apiClient from '@/services/api';

interface EmailDomain {
  id: string;
  domain: string;
  created_at: string;
}

interface EmailDomainsManagerProps {
  language: string;
}

const EmailDomainsManager: React.FC<EmailDomainsManagerProps> = ({ language }) => {
  const [domains, setDomains] = useState<EmailDomain[]>([]);
  const [newDomain, setNewDomain] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isAdding, setIsAdding] = useState(false);
  const { toast } = useToast();
  const { t } = useTranslation();

  useEffect(() => {
    fetchDomains();
  }, []);

  const fetchDomains = async () => {
    setIsLoading(true);
    try {
      // 管理面板使用需要认证的API端点
      const response = await apiClient.get('/admin/email-domains');
      const data = response.data || [];
      setDomains(data);
    } catch (error) {
      console.error('Error fetching email domains:', error);
      toast({
        variant: 'destructive',
        title: t('common.error'),
        description: t('admin.emailDomains.loadFailed'),
      });
      // 如果API调用失败，显示空列表而不是模拟数据
      setDomains([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddDomain = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newDomain.trim()) {
      toast({
        variant: 'destructive',
        title: t('common.error'),
        description: t('admin.emailDomains.enterDomainName'),
      });
      return;
    }

    setIsAdding(true);
    try {
      // 管理面板使用需要认证的API端点
      await apiClient.post('/admin/email-domains', {
        domain: newDomain.trim()
      });

      toast({
        title: t('common.success'),
        description: t('admin.emailDomains.addSuccess'),
      });

      setNewDomain('');
      fetchDomains();
    } catch (error: unknown) {
      console.error('Error adding domain:', error);
      
      let errorMessage = t('admin.emailDomains.addFailed');
      
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response?: { status?: number; data?: { message?: string } } };
        if (axiosError.response?.status === 409) {
          errorMessage = t('admin.emailDomains.domainAlreadyExists');
        } else if (axiosError.response?.data?.message) {
          errorMessage = axiosError.response.data.message;
        }
      }
      
      toast({
        variant: 'destructive',
        title: t('common.error'),
        description: errorMessage,
      });
    } finally {
      setIsAdding(false);
    }
  };

  const handleDeleteDomain = async (domainId: string) => {
    try {
      // 管理面板使用需要认证的API端点
      await apiClient.delete(`/admin/email-domains/${domainId}`);

      toast({
        title: t('common.success'),
        description: t('admin.emailDomains.deleteSuccess'),
      });

      fetchDomains();
    } catch (error) {
      console.error('Error deleting domain:', error);
      toast({
        variant: 'destructive',
        title: t('common.error'),
        description: t('admin.emailDomains.deleteFailed'),
      });
    }
  };

  return (
    <div>
      <h3 className="text-lg font-medium mb-2">
        {t('admin.emailDomains.title')}
      </h3>
      <p className="text-sm text-muted-foreground mb-4">
        {t('admin.emailDomains.description')}
      </p>

      <form onSubmit={handleAddDomain} className="flex gap-2 mb-4">
        <div className="relative flex-1">
          <Mail className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder={t('admin.emailDomains.enterDomain')}
            value={newDomain}
            onChange={(e) => setNewDomain(e.target.value)}
            disabled={isLoading || isAdding}
            className="pl-10"
          />
        </div>
        <Button type="submit" disabled={isLoading || isAdding || !newDomain}>
          <Plus className="h-4 w-4 mr-2" />
          {t('admin.emailDomains.addDomain')}
        </Button>
      </form>

      {isLoading ? (
        <div className="text-center p-8 border rounded-md">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">
            {t('common.loading')}
          </p>
        </div>
      ) : domains.length === 0 ? (
        <div className="text-center p-8 border rounded-md">
          <Mail className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
          <p className="text-muted-foreground">
            {t('admin.emailDomains.noDomainsFound')}
          </p>
        </div>
      ) : (
        <div className="border rounded-md overflow-hidden">
          <table className="min-w-full divide-y divide-border">
            <thead className="bg-muted/50">
              <tr>
                <th className="px-4 py-2 text-left text-sm font-medium">
                  {t('admin.emailDomains.domain')}
                </th>
                <th className="px-4 py-2 text-center text-sm font-medium">
                  {t('common.createdAt')}
                </th>
                <th className="px-4 py-2 text-right text-sm font-medium">
                  {t('admin.emailDomains.actions')}
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-border">
              {domains.map((domain) => (
                <tr key={domain.id} className="hover:bg-muted/50">
                  <td className="px-4 py-2 text-sm">
                    <div className="flex items-center">
                      <Mail className="h-4 w-4 mr-2 text-muted-foreground" />
                      <span className="font-medium">{domain.domain}</span>
                    </div>
                  </td>
                  <td className="px-4 py-2 text-sm text-center text-muted-foreground">
                    {new Date(domain.created_at).toLocaleDateString(
                      language === 'zh' ? 'zh-CN' : 'en-US',
                      { year: 'numeric', month: 'short', day: 'numeric' }
                    )}
                  </td>
                  <td className="px-4 py-2 text-sm text-right">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteDomain(domain.id)}
                      disabled={isLoading || domains.length <= 1}
                      className="h-8 text-destructive hover:text-destructive hover:bg-destructive/10"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          <div className="px-4 py-3 bg-muted/20 border-t">
            <p className="text-sm text-muted-foreground">
              {t('admin.emailDomains.showingItems', {
                start: domains.length > 0 ? 1 : 0,
                end: domains.length,
                total: domains.length
              })}
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default EmailDomainsManager;
