import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>,
  CardDescription,
  CardFooter,
} from '@/components/ui/card';
import { useAppContext } from '@/context/AppContext';
import { useTranslation } from 'react-i18next';
import { <PERSON><PERSON>he<PERSON>, BellDot } from 'lucide-react';
import { useDomainState } from '@/hooks/domain-whitelist/useDomainState';
import { useDomainActions } from '@/hooks/domain-whitelist/useDomainActions';
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Import subcomponents
import DomainForm from './domains/DomainForm';
import DomainSearch from './domains/DomainSearch';
import DomainsList from './domains/DomainsList';
import PendingDomainsList from './domains/PendingDomainsList';

interface DomainWhitelistSectionProps {
  approvedDomains: string[];
}

const DomainWhitelistSection: React.FC<DomainWhitelistSectionProps> = ({ approvedDomains: initialDomains }) => {
  const { language } = useAppContext();
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('approved');

  // 使用状态管理
  const {
    domains,
    setDomains,
    pendingDomains,
    setPendingDomains,
    isLoading,
    setIsLoading,
    fetchDomains
  } = useDomainState(initialDomains);

  // 使用操作方法
  const {
    submitDomain,
    approveDomain,
    rejectDomain,
    toggleDomainApproval,
    deleteDomain
  } = useDomainActions(
    domains,
    setDomains,
    pendingDomains,
    setPendingDomains,
    setIsLoading,
    fetchDomains
  );
  
  const filteredDomains = (domains || []).filter(domain =>
    domain && domain.domain && typeof domain.domain === 'string' &&
    domain.domain.toLowerCase().includes(searchQuery.toLowerCase())
  );
  
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>{t('admin.domainWhitelist.title')}</CardTitle>
            <CardDescription>
              {t('admin.domainWhitelist.description')}
            </CardDescription>
          </div>
          <div className="bg-primary/10 p-2 rounded-full">
            <ShieldCheck className="h-6 w-6 text-primary" />
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <DomainForm 
          onAddDomain={submitDomain}
          isLoading={isLoading}
        />
        
        <Tabs 
          value={activeTab} 
          onValueChange={setActiveTab}
          className="mt-6"
        >
          <TabsList className="mb-4">
            <TabsTrigger value="approved">
              {t('admin.domainWhitelist.approvedDomains')}
            </TabsTrigger>
            <TabsTrigger value="pending" className="relative">
              {t('admin.domainWhitelist.pendingApproval')}
              {pendingDomains && Array.isArray(pendingDomains) && pendingDomains.length > 0 && (
                <span className="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-[10px] text-white">
                  {pendingDomains.length}
                </span>
              )}
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="approved" className="space-y-4">
            <DomainSearch 
              searchQuery={searchQuery}
              onSearchChange={setSearchQuery}
              disabled={isLoading}
            />
            
            <DomainsList 
              domains={filteredDomains}
              onToggleApproval={toggleDomainApproval}
              onDelete={deleteDomain}
              isLoading={isLoading}
              // 根据错误提示，移除了showDelete属性，因为DomainsList组件未定义该属性
            />
            
            {filteredDomains.length > 0 && (
              <div className="px-4 py-3 bg-muted/20 border rounded-md mt-4">
                <p className="text-sm text-muted-foreground">
                  {t('admin.domainWhitelist.showingItems', {
                    start: 1,
                    end: filteredDomains.length,
                    total: (domains || []).length
                  })}
                </p>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="pending" className="space-y-4">
            <div className="bg-amber-50 border border-amber-200 rounded-md p-4 mb-4">
              <div className="flex items-center">
                <BellDot className="h-5 w-5 text-amber-500 mr-2" />
                <h3 className="font-medium text-amber-800">
                  {t('admin.domainWhitelist.domainsAwaitingApproval')}
                </h3>
              </div>
              <p className="text-sm text-amber-700 mt-1">
                {t('admin.domainWhitelist.reviewAndApprove')}
              </p>
            </div>
            
            <PendingDomainsList
              domains={pendingDomains || []}
              onApprove={approveDomain}
              onReject={rejectDomain}
              isLoading={isLoading}
            />

            {pendingDomains && Array.isArray(pendingDomains) && pendingDomains.length > 0 && (
              <div className="px-4 py-3 bg-amber-50 border border-amber-200 rounded-md mt-4">
                <p className="text-sm text-amber-700">
                  {t('admin.domainWhitelist.showingItems', {
                    start: 1,
                    end: pendingDomains.length,
                    total: pendingDomains.length
                  })} - {t('common.domainsAwaitingApproval')}
                </p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default DomainWhitelistSection;
