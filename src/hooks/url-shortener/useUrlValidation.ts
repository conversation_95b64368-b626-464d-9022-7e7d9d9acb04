
import { useState, useEffect } from "react";
import { extractDomain } from "@/utils/urlUtils";

/**
 * Hook for validating URLs against approved domains
 */
export const useUrlValidation = (approvedDomains: string[]) => {
  const [url, setUrl] = useState("");
  const [isValidDomain, setIsValidDomain] = useState(true);

  useEffect(() => {
    if (url) {
      const domain = extractDomain(url);

      if (domain) {
        // 如果没有域名白名单或白名单为空，则允许所有域名
        if (!approvedDomains || approvedDomains.length === 0) {
          setIsValidDomain(true);
        } else {
          // 检查域名是否在白名单中（支持子域名）
          const isValid = approvedDomains.some(approvedDomain => {
            // 完全匹配或者是子域名
            return domain === approvedDomain || domain.endsWith('.' + approvedDomain);
          });
          setIsValidDomain(isValid);
        }
      } else {
        setIsValidDomain(false);
      }
    } else {
      setIsValidDomain(true);
    }
  }, [url, approvedDomains]);

  return {
    url,
    setUrl,
    isValidDomain
  };
};
