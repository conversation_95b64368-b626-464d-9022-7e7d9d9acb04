import { useState, useEffect } from 'react';
import { DomainData } from '@/types/domains';
import * as domainService from '@/services/domainWhitelistService';

export function useDomainState(initialDomains: string[] = []) {
  const [domains, setDomains] = useState<DomainData[]>([]);
  const [pendingDomains, setPendingDomains] = useState<DomainData[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // 获取域名数据
  const fetchDomains = async () => {
    try {
      setIsLoading(true);
      // 获取所有域名（包括等待审核的），管理员权限会在后端自动处理
      const response = await domainService.getDomainWhitelist({
        approved: undefined, // 不过滤审核状态，让后端根据用户权限返回相应数据
        limit: 1000 // 获取更多数据以确保包含所有域名
      });
      const allDomains = response.domains || [];
      
      // 分类域名
      const approved = allDomains
        .filter(d => d.approved)
        .map(domain => ({
          id: domain.id,
          domain: domain.domain,
          approved: domain.approved,
          created_at: domain.created_at,
          user_id: domain.user_id?.toString() || null
        }));
        
      const pending = allDomains
        .filter(d => !d.approved)
        .map(domain => ({
          id: domain.id,
          domain: domain.domain,
          approved: domain.approved,
          created_at: domain.created_at,
          user_id: domain.user_id?.toString() || null
        }));
      
      setDomains(approved);
      setPendingDomains(pending);
    } catch (error) {
      console.error('Error fetching domains:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 初始化时获取数据
  useEffect(() => {
    fetchDomains();
  }, []);

  return {
    domains,
    setDomains,
    pendingDomains,
    setPendingDomains,
    isLoading,
    setIsLoading,
    fetchDomains // 暴露刷新方法
  };
}
