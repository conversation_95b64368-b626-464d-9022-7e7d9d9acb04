about:
  aboutProject: About Project
  aboutUs: About Us
  communityBuilt: Community Built
  communityDriven: Community Driven
  communityDrivenDesc: Built and maintained by the developer community
  communityOriented: Community Oriented - Listen to users and continuously improve
  contribute: Contribute
  description: We're building the future of digital productivity
  faq: FAQ
  features:
    hotList:
      categorizedContent: Categorized trending topics
      checkNow: Check Now
      description: Aggregate trending content from major platforms, get the latest news and trends in one place.
      multiplePlatforms: Multi-platform content aggregation
      realTimeUpdates: Real-time trending content updates
      title: Today's Hot List
    navigation:
      categorizedWebsites: Categorized quality websites
      description: Carefully curated website categories to discover quality resources and improve work efficiency.
      exploreNow: Explore Now
      publicPrivate: Support for public and private collections
      submitResources: Submit quality resources
      title: Website Navigation
    notes:
      description: Capture ideas and organize thoughts with our note system
      organizeByCategories: Organize by categories
      quickCapture: Quick idea capture
      searchFunctionality: Powerful search functionality
      title: Quick Notes and Memos
      tryNow: Try Now
    tempEmail:
      autoExpiration: Automatic expiration and cleanup
      description: Quickly generate temporary email addresses to protect privacy and avoid spam.
      instantCreation: Instant email creation
      noRegistration: No registration required
      title: Temporary Email
      tryNow: Try Now
    todoList:
      description: Organize tasks and boost productivity with our intuitive task manager
      dueDateReminders: Due date reminders
      progressTracking: Progress tracking
      taskPrioritization: Task prioritization system
      title: Todo Task Management
      tryNow: Try Now
    urlShortening:
      analytics: Detailed access statistics
      customCodes: Custom short link codes
      description: Convert long URLs into short, memorable links with custom codes and detailed analytics.
      qrGeneration: Automatic QR code generation
      title: URL Shortening Service
      tryNow: Try Now
  forever: Forever Free
  getStartedFree: Get Started Free
  joinUs: Join Us
  joinUsDesc: Be part of our growing community
  learnMore: Learn More
  license: License
  mission: Our Mission
  missionDescription: We are committed to providing simple, secure, and efficient online tools that make your digital life more convenient. We believe technology should serve people, not the other way around.
  openSource: Open Source
  openSourceDesc: Completely open source, transparent and reliable
  openSourceProject: Open Source Project
  openSourceProjectDesc: A completely open source all-in-one tool platform, community-driven and forever free
  ourFeatures: Our Features
  ourValues: Our Values
  overview:
    description: Explore our comprehensive suite of online tools designed to make your digital life more efficient and convenient.
    hotList:
      description: Get the latest trending content from various platforms
      title: Real-time Hot List Aggregation
    navigation:
      description: Discover and bookmark quality web resources
      title: Curated Website Navigation
    signUp: Sign Up Now
    tempEmail:
      description: Privacy-protecting disposable email solution
      title: Temporary Email Service
    title: Features Overview
    urlShortening:
      description: Quick short link generation with customization and analytics
      title: Smart URL Shortening
  privacyFirst: Privacy First - Your data security is our top priority
  privacyFirstDesc: Protect your data privacy and security
  privacyFocus: Privacy Focus
  privacyFocusDesc: Your data security is our priority
  questions:
    contribute: Can I contribute to this project?
    contributeAnswer: "Absolutely! We welcome community contributions. You can submit issues, suggestions, or code contributions on our GitHub repository."
    dataPrivacy: Is my data safe?
    dataPrivacyAnswer: Absolutely. We use industry-standard encryption to protect your data and never share your personal information with third parties.
    freeServices: Are these services free?
    freeServicesAnswer: Yes, all our core services are completely free. Were committed to providing high-quality tools without any cost.
    newFeatures: Will you add new features?
    newFeaturesAnswer: Yes, we continuously improve and add new features based on user feedback and needs. Stay tuned for our update announcements.
    urlShutdown: What happens to my short URLs if the service shuts down?
    urlShutdownAnswer: We commit to notifying users in advance of any service changes and providing data export options to ensure you dont lose important links.
  signUp: Sign Up Now
  simplicityEase: "Simplicity & Ease - Intuitive interface with no learning curve"
  sourceCode: View Source Code
  transparency: Transparency - Open source code and transparent operations
  viewSource: View Source
  whyChooseUs:
    constantlyEvolving:
      description: We continuously improve and add new features to provide users with better experience and more practical tools.
      title: Constantly Evolving
    fastReliable:
      description: High-performance servers and optimized code ensure fast response and stable service experience.
      title: "Fast & Reliable"
    privacyFocused:
      description: We value your privacy, all data is encrypted to ensure your information security.
      title: Privacy Focused
    title: Why Choose Us
admin:
  addDomain: Add Domain
  addDomainToWhitelist: Add domain to whitelist
  addFeature: Add Feature
  addNewFeature: Add New Feature
  badgeChinese: Badge (Chinese)
  badgeEnglish: Badge (English)
  badgeExampleEn: e.g. NEW
  badgeExampleZh: e.g. 新功能
  banner:
    addLine: Add Line
    advancedConfiguration: Advanced Configuration
    animationSpeed: Animation Speed
    bannerHeight: Banner Height (px)
    basicConfiguration: Basic Configuration
    default: All-in-One Toolbox
    defaultTexts:
      allInOneToolbox: All-in-One Toolbox
      boostProductivity: Boost your productivity
      easyToUse: Easy to use
      professionalReliable: "Professional & Reliable"
    displayStyle: Display Style
    dynamicTextLines: Dynamic Text Lines
    enterText: Enter text to hash
    firstLine: First Line
    flow: Flow
    fourthLine: Fourth Line
    gradient: Gradient
    inline: Inline
    lineSpacing: Line Spacing
    loadConfigFailed: Failed to load configuration
    noDynamicLines: No dynamic lines added. Lines will be generated from basic configuration.
    preview: Preview
    saveConfigSuccess: Configuration saved successfully
    saveConfiguration: Save Configuration
    secondLine: Second Line
    stacked: Stacked
    styleOptions: Style Options
    tailwindGradient: Tailwind gradient
    text: Text
    thirdLine: Third Line
    togglePreview: Toggle Preview
    useSecondLineBreak: Use second line break
  bannerConfiguration: Banner Configuration
  bannerConfigurationDescription: Customize the features and main title displayed in the homepage banner.
  bannerFeatures: Banner Features
  bannerFeaturesDescription: Configure feature cards displayed in the homepage banner
  charts:
    uniqueVisitors: Unique Visitors
    visits: Visits
  customizeFeatureDescription: Customize how this feature appears on the site.
  descriptionChinese: Description (Chinese)
  descriptionEnglish: Description (English)
  domainReviewNotification: Domain will be reviewed by administrators
  domainWhitelist:
    addDomain: Add Domain
    addNewDomain: Add New Domain
    allDomains: All Domains
    approve: Approve
    approvedDomains: Approved Domains
    confirmDelete: Are you sure you want to delete this domain?
    created: Created
    description: Manage the list of allowed domains
    domainsAwaitingApproval: Domains awaiting approval
    enterDomainPlaceholder: Enter domain name (e.g., example.com)
    noDomainsFound: No domains found
    paginationInfo: "Page {{current}} of {{total}}"
    pendingApproval: Pending Approval
    refresh: Refresh
    reject: Reject
    reviewAndApprove: Review and approve user-submitted links
    searchDomainsPlaceholder: Search domains...
    showingItems: "Showing {{start}} to {{end}} of {{total}} domains"
    title: Domain Whitelist Manager
  domains:
    addedSuccessfully: Domain added successfully
    deletedSuccessfully: Domain deleted successfully
    failedToAdd: Failed to add domain
    failedToDelete: Failed to delete link
    failedToLoadWhitelist: Failed to load domain whitelist
    failedToUpdateStatus: Failed to update domain status
    statusUpdatedSuccessfully: Domain status updated successfully
  editFeature: Edit Feature
  emailDomains:
    actions: Actions
    addDomain: Add Domain
    addFailed: Failed to add domain
    addSuccess: Domain added successfully
    deleteFailed: Failed to delete domain
    deleteSuccess: Domain deleted successfully
    description: Manage allowed email domains for temporary email service
    domain: Domain
    domainAlreadyExists: Domain already exists
    enterDomain: Enter domain name (e.g., example.com)
    enterDomainName: Please enter domain name
    loadFailed: Failed to load domains
    noDomainsFound: No domains found
    showingItems: "Showing {{start}} to {{end}} of {{total}} domains"
    title: Email Domain Management
  enterDomainForApproval: Enter domain for approval
  featureGridLayout: Feature Grid Layout
  featureManagement: Feature Management
  features:
    errors:
      mustLoginToSave: You must login before saving configuration
      saveConfigFailed: Failed to save configuration
    featureDeleted: Feature deleted. Remember to save your changes.
    loadBannerTitleFailed: Failed to load banner title configuration
    loadConfigFailed: Failed to load configuration
    newFeature:
      description: Feature description
      title: New Feature
    orderUpdated: Order updated. Remember to save your changes.
    saveConfigFailed: Failed to save configuration
    saveConfigSuccess: Configuration saved successfully
  featuresPerRow: Features Per Row
  fiveFeatures: 5 Features
  fourFeatures: 4 Features
  gridLayoutDescription: Configure how feature cards are displayed in the homepage banner. With 1 row and 6 features, all cards will be shown in a single row.
  icon: Icon
  iconBackground: Icon Background
  link:
    down: Move Down
    failedToFetchLinks: Failed to fetch links
    failedToMoveLink: Failed to move link
    nameUrlCategoryRequired: Name, URL, and category are required
    up: Move Up
  linkModeration:
    approve: Approved
    approved: Approved
    category: Category Name
    error: Error
    failedToApproveLink: Failed to approve link
    failedToLoadSubmissions: Failed to load submissions
    failedToRejectLink: Failed to reject link
    linkApproved: Link Approved
    linkApprovedDesc: Link has been approved successfully
    linkRejected: Link Rejected
    linkRejectedDesc: Link has been rejected successfully
    loadingSubmissions: Loading submissions...
    noPendingSubmissions: No pending submissions
    pending: Pending Approval
    pendingLinkSubmissions: Pending Link Submissions
    recentlyModerated: Recently Moderated
    reject: Rejected
    rejected: Rejected
    submittedBy: "Submitted by:"
    submittedOn: "Submitted on:"
  mainTitleAndDescription: Main Title and Description
  mainTitleChinese: Main Title (Chinese)
  mainTitleEnglish: Main Title (English)
  map:
    visitorMap: Visitor Map
    visitorMapDescription: View geographical distribution of visitors
  navigationManager:
    categories: Categories
    description: Manage navigation categories and links
    links: Links
    moderation: Moderation
    preview: Preview
    title: Navigation Manager
  navigationPreview:
    doneReordering: Done Reordering
    dragAndDropToReorder: Drag and drop to reorder links
    external: "{{name}} - external tool"
    failed: Failed to load emails
    internal: "{{name}} - internal tool"
    loadingPreview: Loading preview...
    noCategoriesFound: No categories found
    noLinksInCategory: "No links available in this category. Add your first link!"
    ping: Ping
    reorderLinks: Reorder Links
    title: Navigation Preview
  numberOfRows: Number of Rows
  oneRow: 1 Row
  optional: (Optional)
  panel:
    description: System management and configuration center
    refresh: Refresh
    title: Admin Panel
  s3Config:
    addConfig: Add S3 Configuration
    bucket: Bucket Name
    bucketName: Bucket Name
    configDescription: Configure S3 storage settings for file uploads
    configName: Configuration Name
    confirmDelete: Confirm Delete
    confirmDeleteDescription: Are you sure you want to delete this S3 configuration? This action cannot be undone.
    createSuccess: S3 configuration created successfully
    customEndpoint: Custom Endpoint (Optional)
    deleteFailed: Failed to delete S3 configuration
    deleteSuccess: S3 configuration deleted successfully
    description: Configure S3 storage settings for file uploads
    editConfig: Edit S3 Configuration
    fetchFailed: Failed to fetch S3 configurations
    keepUnchanged: Leave empty to keep unchanged
    noConfigsFound: No S3 configurations found
    region: Region
    saveFailed: Failed to save S3 configuration
    setAsDefault: Set as Default
    setDefaultFailed: Failed to set default S3 configuration
    setDefaultSuccess: Default S3 configuration set successfully
    testFailed: S3 configuration test failed
    testSuccess: S3 configuration test successful
    title: S3 Configuration
    updateSuccess: S3 configuration updated successfully
    useSSL: Use SSL
  saveChanges: Save Changes
  saveConfiguration: Save Configuration
  section: Section
  selectColumns: Select columns
  selectGradient: Select gradient
  selectIcon: Select icon
  selectRows: Select rows
  selectSection: Select section
  settings:
    defaultUrlExpiration: Default URL Expiration (days)
    description: Manage system settings and configuration
    domainForShortUrls: Domain for Short URLs
    saveSettings: Save Settings
    shortUrlsAlwaysUseCurrentDomain: Short URLs will always use the current domain you're visiting
    systemDefault: System Default
    urlSettings: URL Settings
  show: Show
  showingDomainsCount: "Showing {{filtered}} of {{total}} domains"
  showingUsersInfo: "Showing {{current}}/{{total}} users"
  sixFeatures: 6 Features
  stats:
    categories:
      designResources: Design Resources
      developmentTools: Development Tools
    domainTraffic: Domain Traffic
    shortUrlVisits: Short URL Visits
    statistics: Statistics
    systemUsageAndAnalytics: System usage and analytics
    timeline: Timeline
    visitorDistribution: Visitor Distribution
    visitorDistributionDescription: Geographic distribution of site visitors
  submitDomain: Submit Domain
  submitDomainNotification: Domain will be reviewed by administrators before approval
  threeFeatures: 3 Features
  threeRows: 3 Rows
  titleChinese: Title (Chinese)
  titleEnglish: Title (English)
  twoFeatures: 2 Features
  twoRows: 2 Rows
auth:
  accountLink: Account Link
  accountLinkFailed: Account link failed
  accountLinkResponseError: Account link response format error
  accountLinkSuccess: Account link successful
  alreadyHaveAccount: Already have an account?
  avatar: Avatar
  backToLogin: Back to login page
  backToRegister: Back to registration page
  checkEmailForResetLink: Please check your email for a reset link
  confirmNewPassword: Confirm New Password
  confirmPassword: Confirm Password
  createAccount: Create Account
  createNewAccount: Create new account
  dontHaveAccount: Don't have an account?
  email: Email
  emailNotVerifiedMessage: Your email address is not verified. Please check your email and click the verification link.
  emailNotVerifiedPattern: email not verified
  emailSent: Email sent
  emailVerification: Email Verification
  emailVerificationSuccess: "Email verification successful! Your account has been created."
  enterNewPasswordInstructions: Please enter your new password below
  enterUsername: Enter username
  expiredResetToken: Reset token has expired
  failedToLoadOAuthProviders: Failed to load OAuth providers
  failedToSendResetEmail: Failed to send reset email
  failedToSendVerificationEmail: Failed to send verification email
  failedToSendVerificationEmailFallback: Failed to send verification email
  failedToStartOAuth: Failed to start OAuth login
  forgotPassword: Forgot Password?
  forgotPasswordDescription: Enter your email address and well send you a link to reset your password
  forgotPasswordTitle: Forgot Password
  invalidToken: Invalid authentication token received
  invalidVerificationLink: Invalid verification link
  linkAccount: Link Account
  linkedToExistingAccount: Linked to existing account
  loading: Loading...
  loadingDots: Loading...
  loginDescription: Enter your credentials to access your account
  loginFailed: Login failed. Please check your credentials.
  loginRequired: Login Required
  loginResponseError: Login response format error
  loginSuccess: "Logged in successfully!"
  loginSuccessful: "You have successfully logged in!"
  loginTitle: Login
  loginWith: "Login with {{provider}}"
  missingAccountLinkToken: Missing account link token
  missingAuthCode: Missing authorization code
  missingProviderInfo: Missing provider information
  newAccountCreated: New account created successfully
  newPassword: New Password
  oauthLoginFailed: OAuth login failed
  orContinueWithEmail: or continue with email
  password: Password
  passwordMinLength: Password must be at least 8 characters
  passwordResetEmailSent: Password reset email sent
  passwordUpdateFailed: Failed to update password
  passwordUpdateSuccess: Password updated successfully
  passwordsDoNotMatch: Passwords do not match
  passwordsDontMatch: Passwords do not match
  pleaseEnterUsername: Please enter username
  processingLogin: Processing login...
  redirecting: Redirecting...
  registerDescription: Create a new account to get started
  registerFailed: Registration failed. Please try again.
  registerSuccess: Registration successful
  registerTitle: Register
  registrationFailed: Registration failed, please try again
  rememberPassword: Remember your password?
  resendIn: Resend in
  resendVerificationEmail: Resend verification email
  resetEmailSent: Reset email sent
  resetEmailSentDesc: A password reset email has been sent to your email address
  resetPasswordDescription: Enter your email address to receive a password reset link
  resetPasswordTitle: Reset Password
  sendResetEmail: Send Reset Email
  sendingDots: Sending...
  setUsernameForNewAccount: Please set username to create new account
  stateVerificationFailed: State verification failed, please login again
  updateYourPassword: Update Your Password
  username: Username
  verificationEmailSent: A verification email has been sent to your email address. Please check your inbox and click the verification link.
  verificationExpired: Verification failed, link may have expired
  verificationFailed: Verification failed, please try again
  verificationSuccess: Verification successful
  verifyResetTokenFailed: Failed to verify reset token
  verifying: Verifying your email...
  verifyingEmail: Verifying your email...
  welcomeBack: "Welcome back!"
chooseExpiration: Choose expiration time
common:
  actions: Actions
  actionsLabel: Actions
  active: Active
  activeShortUrls: Active Short URLs
  activeTodos: Active Todos
  add: Add
  addCategory: Add Category
  addLink: Add Link
  addMemo: Add Memo
  addNewTodo: Add New Todo
  addTask: Add Task
  addTodo: Add Todo
  adding: Adding
  admin: Admin Panel
  all: All
  allDates: All Dates
  allDomainSubmissionsProcessed: All domain submissions have been processed
  allPlatformsLoaded: All platforms loaded
  allSubmissionsReviewed: All submissions have been reviewed
  allTags: All Tags
  approve: Approved
  approved: Approved
  approvedDomains: Approved Domains
  approvedDomainsDescription: Display list of all approved domains
  bannerTextConfiguration: Banner Text Configuration
  barChart: Bar Chart
  browseTrendingTopics: Browse trending topics
  cancel: Cancel
  categories: Categories
  category:
    cannotDeleteWithSubcategories: Cannot delete category with subcategories
    createFailed: Failed to create category
    createSuccess: Category created successfully
    deleteFailed: Failed to delete category
    deleteSuccess: Category deleted successfully
    nameRequired: Category name is required
    updateFailed: Failed to update category
    updateSuccess: Category updated successfully
  categoryName: Category Name
  categoryStatistics: Category Statistics
  categoryUpdatedSuccessfully: Category updated successfully
  clear: Clear Search
  clearPriority: Clear Priority
  clickDetails: Click Details
  clicks: 👆 Clicks
  close: Close
  comparison:
    browseCategories: Browse categories
    categoryFiltering: Category filtering
    createShortUrls: Create short URLs
    customCategories: Custom categories
    emailsUpTo30Days: Emails up to 30 days
    feature: Feature
    guestUsers: Guest Users
    multiPlatforms: Multi-platform aggregation
    navigationDirectory: Navigation Directory
    permanentUrls: Permanent URLs
    privateCollections: Private collections
    registeredUsers: Registered Users
    saveHotTopics: Save hot topics
    submitDomains: Submit domains
    submitLinks: Submit links
    tempEmails: Temporary emails
    temporaryEmail: Temporary Email
    todaysHotList: Todays Hot List
    trackClicks: Click tracking
    urlShortening: URL Shortening
    viewHotLists: View hot lists
  confirm: Confirm
  confirmDeleteCategoriesMessage_one: Are you sure you want to delete this category? This action cannot be undone.
  confirmDeleteCategoriesMessage_other: "Are you sure you want to delete these {{count}} categories? This action cannot be undone."
  confirmDeleteUrlMessage: Are you sure you want to delete this short URL? This action cannot be undone.
  confirmDeletion: Confirm Deletion
  confirmRejectSubmission: Are you sure you want to reject this submission?
  confirmRejection: Confirm Rejection
  contentDisplayError: Content display error
  copy: Copy
  copyUrl: Copy URL
  country: Country
  dismiss: Dismiss
  noNewsAvailable: No news available
  create: Create Category
  createCategory: Create Category
  createCategoryFirst: Please create a category first
  createManagePersonalNavigation: Create and manage your personal navigation categories and links
  created: Created
  createdAt: Created At
  createdEmails: Created Emails
  createdUrls: Created URLs
  creating: Creating...
  currentLanguage: Current Language
  customizeFeaturesVisibilityOrder: Customize features visibility and order
  dailyTraffic: Daily Traffic
  dashboard: Dashboard
  dashboardLayout: Dashboard Layout
  dataHasBeenUpdated: Data has been updated
  default: All-in-One Toolbox
  defaultForAllUsers: Default for all users
  defaultLabel: Default
  delete: Delete
  deleteAction: Delete
  deleteSelected: Delete Selected
  deleteUrl: Delete URL
  detailedVisitorTrafficAnalysis: Detailed visitor traffic analysis
  disabled: Disabled
  domain: Domain
  emailAddress: Email Address
  expiresAt: Expires At
  showingEmailsCount: Showing {{start}} - {{end}} of {{total}} emails
  showingDomains: Showing {{start}} to {{end}} of {{total}} domains
  domainActions:
    domainAlreadyExists: Domain {{domain}} already exists
    domainAddedToWhitelist: Domain {{domain}} added to whitelist
    addedMainDomainInstead: Added main domain {{domain}} instead of subdomain
    domainApprovalStatusUpdated: Domain approval status updated
    domainApprovedSuccess: Domain approved successfully
    domainDeletedSuccess: Domain deleted successfully
    domainRejectedAndRemoved: Domain rejected and removed
    failedToAddDomain: Failed to add domain
    failedToApproveDomain: Failed to approve domain
    failedToDeleteDomain: Failed to delete domain
    failedToRejectDomain: Failed to reject domain
    failedToUpdateDomainStatus: Failed to update domain status
  domainData:
    loginRequiredForAdmin: You must be logged in to access admin functions
  domainDetails:
    alreadyWhitelisted: Domain already in whitelist
    cannotBeEmpty: Domain cannot be empty
    noDomainFound: No domain found
    submitFailed: Failed to submit domain
    submittedForApproval: Domain submitted for approval
    tryAdjustingSearch: Try adjusting your search keywords
  domainTraffic: Domain Traffic
  domainVisits: Domain Visits
  domainWhitelist:
    description: Manage the list of allowed domains
    title: Domain Whitelist
  domainsAwaitingApproval: Domains awaiting approval
  dragDropReordering:
    categoriesReorderFailed: Failed to reorder categories
    categoriesReorderedSuccess: Categories reordered successfully
    linksReorderFailed: Failed to reorder links
    linksReorderedSuccess: Links reordered successfully
  dueDate: Due Date
  dueDateFilter: Due Date Filter
  edit: Edit
  editAction: Edit
  editCategory: Edit Category
  editInManager: Edit in Manager
  editLink: Edit Link
  editTodo: Edit Todo
  email: Temp Emails
  emailAlreadyGenerated: Email already generated
  emailAlreadyGeneratedDesc: You already have an active temporary email address
  emailDetails: Email Details
  emailPrefixRequired: Email prefix is required
  emails: Temp Emails
  enabled: Enabled
  enterCategoryName: Enter category name
  enterIconUrl: Enter icon URL
  enterLinkName: Enter link name
  error: Error
  expiration: Expiration
  external: "{{name}} - external tool"
  failedToApproveLink: Failed to approve link
  failedToCreateShortUrl: Failed to create short URL
  failedToCreateTemporaryEmail: Failed to create temporary email
  failedToDeleteCategories: Failed to delete categories
  failedToDeleteLinks: Failed to delete links
  failedToFetchUrls: Failed to fetch URLs
  failedToLoadEmails: Failed to load emails
  failedToLoadFeatures: Failed to load features
  failedToLoadLinks: Failed to load navigation links
  failedToRejectLink: Failed to reject link
  failedToSaveFeatureOrder: Failed to save feature order
  failedToUpdateCategory: Failed to update category
  failedToUpdateLink: Failed to update link
  featureOrderSaved: Feature order saved
  featureOrdering:
    dashboardPreferencesSaved: Dashboard preferences saved
    failedToSaveDashboard: Failed to save dashboard configuration
    failedToSaveHomepage: Failed to save homepage configuration
    homepageConfigSaved: Homepage configuration saved
    loginRequired: You need to be logged in to access this page.
  featuresGrid:
    autoExpiration: Automatic expiration cleanup
    categorizedWebsites: Categorized quality websites
    checkNow: Check Now
    clickAnalytics: Click analytics
    customShortCodes: Custom short codes
    emailFeature: Temporary Email
    emailFeatureDesc: Generate temporary email addresses for privacy
    exploreNow: Explore Now
    multiplePlatforms: Multi-platform content aggregation
    navigationDirectory: Navigation Directory
    navigationDirectoryDesc: Browse and discover useful tools and resources organized by category
    noRegistrationRequired: No registration required
    publicPrivateCollections: Public and private collections
    realTimeUpdates: Real-time Updates
    todaysHotList: Todays Hot List
    todaysHotListDesc: Stay updated with trending content from multiple platforms
    tryNow: Try Now
    urlFeature: URL Shortener
    urlFeatureDesc: Create short, shareable links with analytics
  featuresOrder: Features Order
  getStarted: Get Started Free
  high: High
  highPriority: High Priority
  home:
    designResources: Design Resources
    developmentTools: Development Tools
    exploreNavigation: Explore Navigation Directory
    featuredCategories: Featured Categories
    frequentlyUsed: Frequently Used
    learning: Learning
    links: Links
    navigation: Navigation Directory
    navigationDescription: Browse and discover useful tools and resources
    navigationDirectory: Navigation Directory
    poweredBy: Powered by
    premium: Premium
    productivity: Productivity
    recentlyVisited: Recently Visited
    searchResources: Search resources...
  homepageLayout: Homepage Layout
  hotNews:
    categorizedContent: Categorized Content
    categorizedContentDesc: Browse trending content from different platforms by category
    exploreTrendingTopics: Explore Trending Topics
    loginToViewHotTopics: Login to view hot topics
    noPlatformsAvailable: No platforms available
    realTimeHotTopics: Real-time Hot Topics
    realTimeUpdates: Real-time Updates
    realTimeUpdatesDesc: Get the latest trending topics and trends
    serviceUnavailable: Service temporarily unavailable
    signIn: Sign In
    signInToAccess: Sign in to access the latest trending content and topics
    smartFiltering: Smart Filtering
    smartFilteringDesc: Filter and personalize recommendations based on your interests
    stayUpdated: Stay updated with the latest trends and hot topics
    todaysTrendingTopics: Todays Trending Topics
    viewTopics: View Topics
    viewMoreTopics: View More Topics
  hotNewsRanking: Hot News Ranking
  hotValue: "Hot Value: "
  icon: Icon
  iconLabel: Icon
  iconUrlOptional: Icon URL (optional)
  iconUrlOrLeaveEmpty: Icon URL or leave empty
  inactive: Inactive
  internal: "{{name}} - internal tool"
  internalLink: Internal Link
  items: items
  jumpToLatest: Jump to latest
  lastUpdated: Last updated
  leaveEmptyForTopLevel: Leave empty to create a top-level category
  link:
    createFailed: Failed to create link
    createSuccess: Link created successfully
    deleteFailed: Failed to delete link
    deleteSuccess: Link deleted successfully
    reorderFailed: Failed to reorder links
    reorderSuccess: Links reordered successfully
    updateFailed: Failed to update link
    updateSuccess: Link updated successfully
  linkApproved: Link Approved
  linkApprovedSuccessfully: Link has been approved successfully
  linkName: Link Name
  linkRejected: Link Rejected
  linkRejectedSuccessfully: Link has been rejected successfully
  linkSubmissionModeration: Link Submission Moderation
  linkUpdatedSuccessfully: Link updated successfully
  links: Links
  linksCount: Links Count
  loadPlatformListFailed: Failed to load platform list
  loaded: QR code downloaded
  loadedCount: Loaded
  loading: Loading
  loadingApprovedSubmissions: Loading approved submissions...
  loadingBannerConfiguration: Loading banner configuration...
  loadingMorePlatforms: Loading more platforms...
  loadingPlatforms: Loading platforms
  loadingPlatform: Loading platform data...
  loadingRejectedSubmissions: Loading rejected submissions...
  loadingResources: Loading resources...
  loadingSubmissions: Loading submissions...
  locale: Language
  login: Login
  loginPrompt:
    description: Manage the list of allowed domains
    title: Domain Whitelist
  logout: Logout
  logoutFailed: Logout failed
  logoutSuccess: Logout successful
  low: Allowed Domains
  lowPriority: Low Priority
  mainCategories: Main Categories
  mainCategory: Main Category
  mainLabel: Main
  manage: Manage
  manageNavigationLinksDesc: Manage your navigation links and categories
  medium: Medium
  messagesCount: Messages
  mediumPriority: Medium Priority
  memoDetails: Memo Details
  memoList: Memo List
  memos: Memos
  messages:
    error:
      emptyInput: Input cannot be empty
      generationError: Error during generation
    success:
      copiedToClipboard: Copied to clipboard
    errors:
      base64Conversion: Base64 conversion error
      emptyInput: Input cannot be empty
      generationError: Error during generation
      invalidBase64: Invalid Base64 format
    hash:
      algorithm: Algorithm
      description: Generate hash values for text
      enterTextToHash: Enter text to hash
      generateHash: Generate Hash
      hashResult: Hash Result
      inputText: Input Text
      title: Hash Generator
    history:
      decode: Decode
      encode: Encode
    tools:
      base64Converter: Base64 Converter
      base64EncodingWillAppearHere: Base64 encoding will appear here
      convertBetweenPlainTextAndBase64: Convert between plain text and Base64
      copy: Copy
      decode: Decode
      decodedTextWillAppearHere: Decoded text will appear here
      encode: Encode
      enterBase64ToDecode: Enter Base64 to decode
      enterTextToEncode: Enter text to encode
      inputBase64: Input Base64
      inputText: Input Text
      outputBase64: Output Base64
      outputText: Output Text
      urlSafeMode: URL Safe Mode
  mode: Mode
  mustBeLoggedIn: You must be logged in
  myMemos: My Memos
  myPersonalNavigation: My Personal Navigation
  mySubmissions: My Submissions
  myTasks: My Tasks
  name: Name
  nameLabel: Name
  navigation:
    about: About
    darkMode: Dark Mode
    featuresIntro: Features Intro
    lightMode: Light Mode
    logoAlt: Logo
    memoAndTodo: Memo & Todo
    toggleLanguage: Toggle Language
    aboutUs: About Us
    all: All
    allCategories: All Categories
    allResources: All Resources
    categories:
      aiTools: AI Tools
      deployment: Deployment
      designTools: Design Tools
      devTools: Dev Tools
      images: Images
      productivity: Productivity
    categoriesLabel: Categories
    categoryManagement: Category Management
    categoryManagementDesc: Smart categorization, easy management
    clearSearch: Clear Search
    connectionFailed: Connection failed
    createPersonal: Create Personal Navigation
    exploreNow: Explore Now
    external: "{{name}} - external tool"
    featured: Featured
    filterByCategory: Filter by Category
    internal: "{{name}} - internal tool"
    loadingData: Loading data...
    myDirectory: My Navigation Directory
    noCategoriesAvailable: No categories available
    noLinksInCategory: "No links available in this category. Add your first link!"
    noResourcesInCategory: No resources found in this category
    noResultsFound: "No results found for \\{{query}}\\"
    online: Online Tools
    open: Open
    openInNewTab: Open in new tab
    personal: Personal Navigation
    quickAdd: Quick Add
    quickBookmarks: Quick Bookmarks
    quickBookmarksDesc: One-click bookmarking, instant access
    quickSearch: Quick search...
    recentlyAdded: Recently Added
    recommended: Recommended
    searchPlaceholder: Search websites and tools...
    showingResourcesInCategory: "Showing resources in {{category}}"
    smartDirectory: Smart Navigation Directory
    smartDirectoryDescription: Build your personalized website navigation with intelligent categorization and efficient access to your favorite websites and tools
    smartSearch: Smart Search
    smartSearchDesc: Quick locate, efficient search
    socialSharing: Social Sharing
    socialSharingDesc: Share recommendations, grow together
    tryDifferentCategory: Try browsing a different category
    unavailable: Service unavailable
    viewAllCategories: View all categories
  navigationCategoryAnalytics: Navigation Category Analytics
  navigationManagement: Navigation Management
  never: Never
  new: New account created successfully
  newsCount: "News Count: "
  no: No
  noActiveTodos: No active todos
  noApprovedDomainsFound: No approved domains found
  noApprovedSubmissions: No approved submissions
  noCategoriesFoundCreateFirst: No categories found, please create one first
  noCategoriesFoundData: No categories found
  noContent: No content
  noCountryDataAvailable: No country data available
  noDataFallback:
    locationDataDescription: Location data will appear here when available
    noLocationDataAvailable: No location data available
    reloadMap: Reload Map
  noDate: No Date
  noDomainTrafficDataAvailable: No domain traffic data available
  noDomainsFound: No domains found
  noDomainsInWhitelist: No domains in whitelist
  noEmailsReceivedYet: No emails received yet
  noHotListDataAvailable: No hot list data available
  noLabel: No
  noLinksFoundAddFirst: "No links found in this category. Add your first link!"
  noMatchingMemos: No matching memos
  noMatchingTasks: No matching tasks
  noMemosYet: No memos yet
  noPageVisitDataAvailable: No page visit data available
  noParentTopLevel: -- No Parent (Top Level) --
  noPendingDomains: No pending domains
  noPendingSubmissions: No pending submissions
  noRejectedSubmissions: No rejected submissions
  noShortUrlVisitsDataAvailable: No short URL visits data available
  noShortUrlsCreatedYet: No short URLs created yet
  noSubject: No subject
  noTasksYet: No tasks yet
  noTemporaryEmailsCreated: No temporary emails created yet
  noTimelineDataAvailable: No timeline data available
  noTodosYet: No todos yet
  noUsersFound: No users found
  notApproved: Not Approved
  openUrl: Open URL
  orderUpdatedRememberSave: Order updated. Remember to save your changes.
  originalUrl: Original URL
  overdue: Overdue
  pagePopularity: Page Popularity
  pageViewsPV: Page Views (PV)
  parentCategory: Parent Category (Optional)
  parentCategoryOptional: Parent Category (Optional)
  pending: Pending Approval
  pickADate: Pick a date
  pieChart: Pie Chart
  platformDistribution: Platform distribution and hot news rankings
  pleaseEnterUrl: Please enter a URL
  pleaseSelectCategory: Please select a category
  pleaseWait: Please wait
  powerfulFeatures: Powerful Features
  preview: Preview
  priority: Priority
  profile: Profile
  progress: Progress
  public: Public
  qrCode: QR code downloaded
  qrCodeForYourUrl: QR Code for Your URL
  recentItems: Recent Items
  recentMemos: Recent Memos
  recentMessages: Recent Messages
  refreshAll: Refresh All
  refreshComplete: Refresh complete
  refreshDataFailed: Failed to refresh data
  refreshingData: Refreshing data...
  register: Register
  registeredUsers: Registered Users
  reject: Rejected
  rejected: Rejected
  reload: Reload
  reset: Reset to Default
  resetToDefaultRememberSave: Reset to default. Remember to save your changes.
  reviewAndApproveLinks: Review and approve user-submitted links
  role: Role
  saveChanges: Save Changes
  saveLayout: Save Layout
  saveOrder: Save Order
  saving: Saving
  search: Search tasks...
  searchDomains: Search domains...
  searchMemosPlaceholder: Search memos...
  searchTasksPlaceholder: Search tasks...
  searchTodosAndMemos: Search todos and memos...
  searchUsers: Search users
  selectAll: Select All
  selectCategory: Select Category
  selectCategoryItem: Select category item
  selectCategoryToViewLinks: Select a category to view its links
  shortUrl: Short URLs
  shortUrlVisits: Short URL Visits
  shortUrls: Short URLs
  showingUsersCount: "Showing {{count}} users"
  showingUsersCount_one: "Showing {{count}} user"
  showingUsersCount_other: "Showing {{count}} users"
  siteVisitorStatistics: Site Visitor Statistics
  statistics: Statistics
  status: Status
  subLabel: Sub
  subcategories: Subcategories
  submit: Submit
  submitDomain: Submit Domain
  submitDomainForWhitelist: Submit domain for whitelist
  submitDomainWhitelistDescription: Submit domain to whitelist for review
  submitted: Submitted links will appear here
  submittedBy: "Submitted by:"
  submitting: Submitting...
  success: Copied to clipboard
  superAdmin: Super Admin
  systemSettings: System Settings
  tags: Tags
  tasks: Tasks
  tempEmail:
    createFailed: Failed to create temporary email
    createSuccess: S3 configuration created successfully
    noEmailsYet: No emails yet
  tempEmails: Temporary Emails
  tempMailbox:
    autoDeleteNotice: Emails will be automatically deleted after 24 hours
    cancel: Cancel
    copiedToClipboard: Copied to Clipboard
    copyEmailFailed: Failed to copy email address
    copyFailed: Copy Failed
    copyToClipboard: Copy to Clipboard
    inboxFor: "Inbox for {{email}}"
    debugMessages:
      copyToClipboardFailed: Failed to copy to clipboard
      fetchEmailsFailed: Failed to fetch emails
      generateTempEmailFailed: Failed to generate temporary email
    delete: Delete
    deleteConfirmation: Are you sure you want to delete this temporary email? This will delete all related emails.
    deleteEmail: Delete Email
    deleteTempEmail: Delete Temporary Email
    description: Generate temporary email addresses to receive emails
    emailAddressCopied: Email address copied
    emailDeleted: Email Deleted
    emailDeletedDesc: Temporary email has been successfully deleted
    emailGenerated: Email Generated
    emailGeneratedDesc: Temporary email address has been successfully generated
    fetchEmailsFailed: Failed to fetch emails
    from: "From: "
    generateEmail: Generate Email
    generateFailed: Failed to generate email
    inbox: Inbox
    noMessages: No messages
    noTempEmail: No temporary email
    noTempEmailDesc: Click the button below to generate a temporary email address
    selectEmail: Select an email to view details
    tempEmailInbox: Temporary Email Inbox
    title: Temporary Mailbox
    validFor24Hours: Valid for 24 hours
    viewAll: View All
    viewInbox: View Inbox
    yourTempEmail: Your Temporary Email
  temporaryEmailCreated: Temporary email created successfully
  temporaryEmails: Temporary Emails
  today: Today
  todayDate: Today
  todaysHotList: Todays Hot List
  todaysHotListAnalytics: Todays Hot List Analytics
  todoTitle: Todo Title
  todosAndMemos: "Todos & Memos"
  tomorrow: Tomorrow
  totalClicks: Total Clicks
  totalUsers: Total Users
  totalVisits: Total Visits
  trackManageShortUrls: Track and manage your short URLs
  trackSubmittedNavigationLinks: Track your submitted navigation links
  trafficByDomain: Traffic by Domain
  tryAdjustingSearchOrAddDomain: Try adjusting your search or add a new domain
  tryAgain: Try again
  type: Type
  typeLabel: Type
  unexpectedError: An unexpected error occurred
  uniqueVisitors: Unique Visitors
  uniqueVisitorsUV: Unique Visitors (UV)
  unknown: Unknown
  unverified: Unverified
  upcoming: Upcoming
  update: S3 configuration updated successfully
  url: URL
  urlCopiedToClipboard: URL copied to clipboard
  urlDetails: URL Details
  urlLabel: URL
  urlShortenedSuccessfully: URL shortened successfully
  urlShortener:
    dailyLimitReached: "Daily limit reached ({{limit}} URLs). Please try again tomorrow."
    domainNotInWhitelist: Domain not in whitelist
    loginRequiredForPermanent: Login required for permanent URLs
    pleaseEnterUrl: Please enter a URL
    urlShortenFailed: Failed to shorten URL
    urlShortenedSuccess: URL shortened successfully
  urlStats: URL Statistics
  urlVisits: URL Visits
  urls: URLs
  userData:
    failedToDeleteUser: Failed to delete user
    failedToLoadUsers: Failed to load users
    failedToUpdateUser: Failed to update user
    userDeletedSuccess: User deleted successfully
    userUpdatedSuccess: User updated successfully
  userDetails: User Details
  userManagement: User Management
  userManagementDescription: Manage system users
  username: Username
  users: User Management
  verified: Verified
  view: "View:"
  viewAllReceivedEmails: View all received emails
  viewAllTodosAndMemos: View all todos and memos
  visibilityUpdatedRememberSave: Visibility updated. Remember to save your changes.
  visible: Visible
  visitorMap: Visitor Map
  visits: Visits
  yes: Yes
  yesLabel: Yes
  yourTemporaryEmailAddresses: Your Temporary Email Addresses
  yourUrls: Your URLs
createAccount: Create Account
cta:
  allFeatures: All Features
  alwaysFree: Always Free
  completelyFree: Completely Free
  exploreFeatures: Explore Features
  getStartedDescription: Experience our all-in-one tool platform immediately, no credit card required, completely free
  getStartedFree: Get Started for Free
  instantAccess: Instant Access
  limitedOffer: Limited Offer
  noCredit: No Credit Card
  openSource: Open Source
  startFreeNow: Start Free Now
dashboard:
  copiedToClipboard: Copied to Clipboard
  description: Manage your short URLs and temporary emails
  emailDeletedSuccessfully: Email deleted successfully
  failedToCreateTempEmail: Failed to create temporary email
  failedToDeleteEmail: Failed to delete email
  failedToDeleteUrl: Failed to delete URL
  failedToLoadData: Failed to load data
  features:
    dashboardLayout: Dashboard Layout
    myNavigation: My Navigation
    temporaryEmail: Temporary Email
    urlShortener: URL Shortener
  noUserIdAvailable: No user ID available
  pleaseLoginFirst: Please login first
  tempEmailCreateNotImplemented: Temporary email creation not implemented
  tempEmailCreated: Temporary email created
  tempEmailDeleteNotImplemented: Temporary email deletion not implemented
  tempEmailFeatureNotAvailable: Temporary email feature not available
  tempEmailsApiNotAvailable: Temporary emails API not available
  tempMailboxApiNotImplemented: Temporary mailbox API not implemented
  title: Dashboard
  urlDeletedSuccessfully: URL deleted successfully
dragDropInstructions: Drag and drop to reorder links
emailExpiration: Email Expiration
emailExpirationDesc: Temporary emails automatically expire for security
emailExpirationOption1: 24 hours - Quick verification
emailExpirationOption2: 7 days - Short-term use
emailExpirationOption3: 30 days - Extended projects
emailFeature1Desc: Generate temporary email addresses instantly
emailFeature1Title: Instant Email Creation
emailFeature2Desc: Keep your real email address private
emailFeature2Title: Privacy Protection
emailFeature3Desc: Avoid unwanted emails in your main inbox
emailFeature3Title: Spam Prevention
emailFeature4Desc: Use without creating an account
emailFeature4Title: No Registration Required
emailFeature5Desc: Create multiple temporary emails
emailFeature5Title: Multiple Email Support
emailFeature6Desc: Emails are automatically deleted after expiration
emailFeature6Title: Automatic Cleanup
emails:
  createEmail: Create Email
  createEmailDescription: Generate a temporary email address for your needs.
emptyState:
  categoryWaitingForResources: This category is waiting for amazing resources to be added
  clearSearch: Clear Search
  discoverAmazingWebsites: Discover amazing websites and tools, or contribute your favorites
  manageNavigation: Manage Navigation
  refresh: Refresh
  tryAdjustingSearch: Try adjusting your search terms or explore different categories
  welcomeToNavigationHub: "Welcome to Navigation Hub!"
enterLongUrl: Enter your long URL
error:
  accessForbidden: Access Forbidden
  authenticationRequired: Authentication required
  createAccount: Create Account
  goToHome: Go to Homepage
  login: Login
  loginRequired: You need to be logged in to access this page.
errors:
  pageNotFound: Page Not Found
  pageNotFoundDescription: The page you are looking for doesnt exist or has been moved.
features:
  arrangeItems: Arrange Items
  arrangeItemsDescription: Drag to reorder feature items and set their visibility
  component:
    name: Name
  dragDropInstructions: Drag and drop items to reorder them. Dont forget to save your changes.
  resetToDefault: Reset to Default
  visible: Visible
form:
  allFieldsRequired: All fields are required
generateTempEmail: Generate temporary email
getShortUrl: Get your short URL
home:
  features:
    navigationDirectory:
      description: Browse curated website collections and tools
      linkText: Explore Navigation
      title: Navigation Directory
    temporaryEmail:
      linkText: Get Temp Email
    todaysHotList:
      description: Trending content from major platforms
      linkText: View Hot Topics
      newLabel: New
      title: Today's Hot List
    urlShortening:
      linkText: Try URL Shortener
  floatingWidget: Floating Widget
  floatingWidgetDescription: Access your todos and memos from anywhere
  openTodoMemo: "Open Todo & Memo"
  quickNotes: Quick Notes
  quickNotesDescription: Capture and organize your thoughts instantly
  taskManagement: Task Management
  taskManagementDescription: Organize and track your daily tasks
  todoMemoDescription: Organize tasks and capture ideas efficiently
  todoMemoManager: "Todo & Memo Manager"
hotNews:
  analytics: Data Analytics
  analyticsDesc: Gain insights into trending data
  dataSource: Data Source
  externalAPI: External API
  failedToLoad: Failed to load
  fetchFailed: Fetch failed
  fetchSuccess: Fetch success
  fetchingData: Fetching data
  localData: Local data
  noData: No data
  noDataAvailable: No data available
  platformCategories:
    entertainment: Entertainment
    news: News
    others: Others
    socialMedia: Social Media
    technology: Technology
  refreshing: Refreshing
  serviceNotice: Service Notice
  customizeFeeds: Customize Your Feed
  description: Aggregated trending news
  entertainment: Entertainment
  errorLoadingNews: Error loading news
  exploreNow: Explore Now
  failedToLoadPlatforms: Failed to load platforms
  hotNews: Hot News
  hotness: Hotness
  loadingDescription: Loading hot news data, please wait...
  lastUpdate: Last Update
  multiPlatform: Multi-Platform Aggregation
  multiPlatformDesc: Gather trending topics from major platforms
  noHotTopicsAvailable: No hot topics available
  noNewsAvailable: No news available
  others: Others
  platforms:
    "/36kr": 36Kr
    "/baidu": Baidu Hot Search
    "/bilibili": Bilibili
    "/douban-movie": Douban Movies
    "/douyin": Douyin Trends
    "/hupu": Hupu
    "/huxiu": Huxiu
    "/ithome": IT Home
    "/juejin": Juejin
    "/netease-news": NetEase News
    "/weibo": Weibo Hot Search
    "/zhihu": Zhihu Hot List
    list: Platforms
  realTimeUpdates: Real-Time Updates
  realTimeUpdatesDesc: Get the latest updates in real time
  refresh: Refresh
  refreshPlatform: Refresh
  retry: Retry
  retrying: Retrying...
  technology: Technology
  title: Hot News
  todaysHotList: Todays Hot List
  todaysTrending: Today's Trending
  trending: Trending
  trendingDesc: Track trending topic patterns
  trendingToday: Today's Trending Topics
  trendingTodayDescription: Real-time aggregation of trending topics from across the web, stay updated with the latest trends and discover valuable content
  view: View
index:
  addLinks: Add Links
  exploreNavigationDirectory: Explore Navigation Directory
  heroDescription: Explore a powerful collection of tools designed to simplify your digital life, including URL shorteners, temporary emails, navigation management, and real-time trending content.
  heroSubtitle: All-in-One Platform
  heroTitle: Your Digital Hub
  hot: Hot
  hotListsDescription: Stay updated with trending topics
  myNavigation: My Navigation
  navigationDirectoryDescription: Discover useful websites and tools, or add your own favorites for quick access
  new: New
  noHotTopicsAvailable: No hot topics available
  noNavigationLinks: You haven't created any navigation links yet
  todaysHotLists: Today's Hot Lists
  todaysHotTopics: Today's Hot Topics
  viewAllHotTopics: View All Hot Topics
  viewAllNavigation: View All Navigation
  viewHotLists: View Hot Lists
  viewNavigationDirectory: View Navigation Directory
joinUs: Join Us
joinUsDesc: Be part of our growing community
layout:
  allRightsReserved: All rights reserved
  contact: Contact
  privacy: Privacy
  terms: Terms
learnMoreFeatures: Learn More About Features
linkSubmission:
  category: Category
  enterLinkName: Enter link name
  failedToCreateLink: Failed to create link
  fillAllRequiredFields: Please fill in all required fields
  iconUrlOptional: Icon URL (optional)
  iconUrlOrLeaveEmpty: Icon URL or leave empty
  internalLink: Internal Link
  internalLinkDescription: (Check if this link points to a page within this site)
  linkName: Link Name
  linkSubmittedForReview: Link submitted for review
  mustBeLoggedInToSubmit: You must be logged in to submit a link
  noCategoriesAvailable: No categories available
  noCategoriesDescription: There are no public categories available for submissions at this time.
  selectAppropriateCategory: Select the most appropriate category for your link
  submitLinkForReview: Submit Link for Review
  submitNewLink: Submit New Link
  submitting: Submitting...
  url: URL
  urlPlaceholder: "https://example.com"
maxDaysLimit: Maximum 30 days for security
memo:
  displayError: Content display error
  noContent: No content
navigation:
  about: About
  aboutUs: About Us
  addAnotherLink: Add Another Link
  addLink: Add Link
  addLinks: Add Links
  addNewLink: Add New Link
  all: All
  allCategories: All Categories
  allLinks: All Links
  approved: Approved
  autoDetectingFavicon: Auto-detecting favicon...
  categories:
    aiTools: AI Tools
    deployment: Deployment
    designTools: Design Tools
    devTools: Dev Tools
    images: Images
    productivity: Productivity
  category: Category Name
  categoryKeywords:
    design: design
    dev: dev
    develop: develop
    edu: edu
    entertain: entertain
    learn: learn
    news: news
    shop: shop
    social: social
    tool: tool
  categoryManagement: Category Management
  categoryManagementDesc: Smart categorization, easy management
  connectionFailed: Connection failed
  createLink: Create Link
  createPersonal: Create Personal Navigation
  darkMode: Dark Mode
  deleteLink: Delete Link
  editLink: Edit Link
  enterLinkName: Enter link name
  exploreNow: Explore Now
  exploreResource: Click to explore this resource
  external: External
  failedToCreateLink: Failed to create link
  failedToDeleteLink: Failed to delete link
  failedToLoadLinks: Failed to load navigation links
  failedToSubmitLink: Failed to submit link to public directory
  failedToSubmitLinkToPublic: Failed to submit link to public area
  failedToSubmitLinks: Failed to submit links
  failedToUpdateLink: Failed to update link
  faviconWillBeAutoDetected: Favicon will be auto-detected
  featured: Featured
  featuresIntro: Features
  filterByCategory: Filter by Category
  fullNavigationPage: Full Navigation Page
  home: Home
  iconPlaceholder: Enter icon URL or leave empty
  iconUploader:
    add: Add
    addIcon: Add Icon
    addIconButton: Add Icon
    cancel: Cancel
    enterIconUrl: Enter icon URL
    failedToLoadImage: Failed to load image. Please check the URL.
    orPasteUrl: "Or paste a publicly accessible image URL. Recommended size: 64x64px."
    pleaseEnterIconUrl: Please enter an icon URL
    preview: Preview
    uploadOrAddIcon: Upload or add icon
  iconUrl: Icon URL or leave empty
  iconUrlOrLeaveEmpty: Icon URL or leave empty
  internal: Internal
  internalLink: Internal Link
  layout:
    all: All
    discoverWebsites: Discover amazing websites and tools
    navigationHub: Navigation Hub
    searchPlaceholder: Search websites and tools...
    sortOptions:
      az: 📝 A-Z
      clicks: 👆 Clicks
      date: 📅 Date
      hot: 🔥 Hot
      new: 🕒 New
  linkCard:
    visit: Visit
  linkDeletedSuccessfully: Link deleted successfully
  linkName: Link Name
  linkSubmittedForReview: Link submitted for review
  linkSubmittedSuccessfully: Link submitted to public directory successfully
  linkSubmittedToPublic: Link submitted to public area
  linkUpdatedSuccessfully: Link updated successfully
  linkUrl: Link URL
  links: Links
  linksList: Links
  loadingSubcategories: Loading subcategories...
  logoAlt: Logo
  main: Domain
  mainCategory: Main Category
  moveDown: Move down
  moveUp: Move up
  myDirectory: My Navigation Directory
  navigation: Navigation
  navigationCenter: Navigation Center
  navigationHub: Navigation Hub
  navigationHubDescription: Discover and explore curated collections of useful websites and tools
  noCategoriesAvailable: No categories available
  noCategoriesFoundCreateFirst: No categories found, please create one first
  noLinksInCategory: "No links available in this category. Add your first link!"
  noSubmittedLinksYet: No submitted links yet
  onlineTools: Online Tools
  pending: Pending Approval
  personalNavigation: Personal Navigation
  pleaseAddAtLeastOneLink: Please add at least one link
  pleaseSelectCategoryFirst: Please select a category first to manage links.
  quickAdd: Quick Add
  quickBookmarks: Quick Bookmarks
  quickBookmarksDesc: One-click bookmarking, instant access
  quickSearch: Quick search...
  recentlyAdded: Recently Added
  recommended: Recommended
  rejected: Rejected
  selectMainCategory: Select a main category
  selectPublicCategory: Select Public Category
  selectSubcategory: Select subcategory
  smartDirectory: Smart Navigation Directory
  smartDirectoryDescription: Build your personalized website navigation with intelligent categorization and efficient access to your favorite websites and tools
  smartSearch: Smart Search
  smartSearchDesc: Quick locate, efficient search
  socialSharing: Social Sharing
  socialSharingDesc: Share recommendations, grow together
  sortOrderUpdateFailed: Failed to update sort order
  sortOrderUpdated: Sort order updated
  sub: Submit
  subcategory: Subcategory
  submitLink: Submit Link
  submitLinkToPublicDescription: Submit this link to the public navigation directory. An administrator will review your submission.
  submitLinks: Submit links
  submitToPublicDirectory: Submit to Public Directory
  submittedLinksWillAppearHere: Submitted links will appear here
  timeUnit: ms
  title: Navigation Center
  description: Modern navigation center for quick access to common tools and services
  todaysHotList: Today's Hot List
  toggleLanguage: Toggle Language
  toggleMenu: Toggle Menu
  urlPlaceholder: "https://example.com"
  searchCards: Search navigation cards...
  loadFailed: Load failed
  loadFailedDesc: Unable to load navigation data
  orderUpdated: Order updated
  orderUpdatedDesc: Card order has been successfully updated
  noCardsFiltered: No cards match the criteria
  noCardsAvailable: No navigation cards available
  addFirstCard: Add first card
  cardUpdatedDesc: Navigation card updated successfully
  cardAdded: Card added
  cardAddedDesc: New navigation card added successfully
  connectivityCheckComplete: Connectivity check complete
  connectivityCheckCompleteDesc: Checked {{total}} external links
  activeOnly: Active only
  downOnly: Down only
  internalOnly: Internal links only
  externalOnly: External links only
  checkConnectivityTooltip: Check connectivity of all external links
  type: Type
  filters: Filters
  allStatus: All status
  allTypes: All types
  personal: Personal
  public: Public
  
  # Navigation System v2 Translations
  searchPlaceholder: Search navigation cards...
  filterBy: Filter by
  viewType: View type
  gridView: Grid view
  listView: List view
  compactView: Compact view
  cardSize: Card size
  small: Small
  medium: Medium
  large: Large
  columns: Columns
  showDescriptions: Show descriptions
  showRatings: Show ratings
  showStatus: Show status
  
  # Card operations
  addCard: Add card
  editCard: Edit card
  deleteCard: Delete card
  cardTitle: Card title
  cardDescription: Card description
  cardUrl: Card URL
  cardIcon: Card icon
  cardTags: Tags
  isInternal: Internal link
  isPublic: Public card
  cardWidth: Card width
  cardHeight: Card height
  cardPosition: Card position
  sortOrder: Sort order
  
  # Category operations
  addCategory: Add category
  editCategory: Edit category
  deleteCategory: Delete category
  categoryColor: Category color
  parentCategory: Parent category
  publicCategory: Public category
  
  # Rating
  rateCard: Rate card
  rating: Rating
  averageRating: Average rating
  userRatings: User ratings
  ratingSubmitted: Rating submitted successfully
  
  # Connectivity check
  checkConnectivity: Check connectivity
  checking: Checking
  lastChecked: Last checked
  responseTime: Response time
  exitEdit: Exit edit
  checkingConnectivity: Checking connectivity... {{current}}/{{total}}
  selectedCards: Selected {{count}} cards
  addNewCard: Add New Card
  iconView: Icon View
  canvasView: Canvas View
  addApp: Add App
  addCardDesc: Create or edit navigation cards with category management and tag support
  url: URL
  titlePlaceholder: Enter website title
  descriptionPlaceholder: Brief description of this website or tool
  deleteSelected: Delete selected
  allCards: All cards
  myCards: My cards
  publicCards: Public cards
  owner: Owner
  
  # Drag and layout
  editMode: Edit mode
  exitEditMode: Exit edit mode
  dragToReorder: Drag to reorder
  resizeCard: Resize card
  moveCard: Move card
  layoutSaved: Layout saved
  
  # Status and errors
  success: Success
  error: Error
  loading: Loading
  noCards: No cards
  noCategories: No categories
  cardCreated: Card created successfully
  cardUpdated: Card updated successfully
  cardDeleted: Card deleted successfully
  categoryCreated: Category created successfully
  categoryUpdated: Category updated successfully
  categoryDeleted: Category deleted successfully
  
  # Filter options
  allOwners: All sources
  activeCards: Active cards
  downCards: Down cards
  internalLinks: Internal links
  externalLinks: External links
  
  # API integration
  apiIntegration: API integration
  integrationType: Integration type
  endpoint: Endpoint
  authToken: Auth token
  refreshInterval: Refresh interval
  lastSynced: Last synced
  syncNow: Sync now
navigationPreview:
  cancel: Cancel
  doneReordering: Done Reordering
  edit: Edit
  editLink: Edit Link
  error: Error
  external: External
  failed: Failed
  failedToUpdateSortOrder: Failed to update sort order
  icon: Icon
  internal: Internal
  linkUpdateFailed: Failed to update link
  linkUpdatedSuccess: Link updated successfully
  loadingPreview: Loading preview...
  name: Name
  navigationPreview: Navigation Preview
  noCategoriesFound: No categories found
  noCategoryLinks: No links in this category
  ping: Ping
  recommend: Recommend
  recommendLink: Recommend Link
  recommendationFailed: Failed to submit recommendation
  recommendationReview: Your recommendation will be reviewed by administrators before being added to the public directory.
  recommendationSubmitted: Recommendation submitted successfully
  reorderLinks: Reorder Links
  saveChanges: Save Changes
  selectCategory: Select Category
  sortOrderUpdated: Sort order updated
  submitRecommendation: Submit Recommendation
  submitting: Submitting...
  timeUnit: ms
  url: URL
onlineTools:
  categories:
    all: All
    design: Design Tools
    development: Development Tools
    popular: Popular
    recent: Recent
    security: Security Tools
    text: Text Tools
    utility: Utility Tools
    web: Web Tools
  clearSearch: Clear Search
  colorConverter:
    colorName: Color Name
    colorPicker: Color Picker
    convert: Convert
    copied: Copied
    description: Convert between different color formats
    format: Format
    inputColor: Input Color
    title: Color Converter
    value: Value
  dataProcessing: Data Processing
  description: A collection of useful online tools
  dialogMode: Dialog Mode
  encryption:
    algorithm: Algorithm
    algorithms:
      aes: AES
      des: DES
      tripleDesEde: 3DES-EDE
      rsa: RSA
      ecc: ECC
      xor: XOR Cipher
    asymmetric: Asymmetric
    ciphertext: Ciphertext
    copy: Copy
    decrypt: Decrypt
    decryptPlaceholder: Enter text to decrypt...
    decryptedResultPlaceholder: Decrypted result will appear here...
    description: Advanced encryption and decryption tool supporting multiple algorithms
    encrypt: Encrypt
    encryptPlaceholder: Enter text to encrypt...
    encryptedResultPlaceholder: Encrypted result will appear here...
    encryptionType: Encryption Type
    errors:
      decryptionFailed: Decryption failed
      encryptionFailed: Encryption failed
      inputRequired: Input text is required
      invalidKey: Invalid key format
      keyRequired: Encryption key is required
      privateKeyRequired: Private key is required
      processingError: Processing error occurred
      publicKeyRequired: Public key is required
      textRequired: Text is required
    generateIV: Generate IV
    generateKey: Generate Key
    generateKeyPair: Generate Key Pair
    hide: Hide
    initializationVector: Initialization Vector
    ivPlaceholder: Enter IV (optional)...
    key: Key
    keyPlaceholder: Enter encryption key...
    keySize: Key Size
    modes:
      cbc: CBC
      cfb: CFB
      ctr: CTR
      ecb: ECB
      gcm: GCM
      ofb: OFB
    mode: Mode
    padding: Padding
    paddings:
      ansix923: ANSI X9.23
      iso10126: ISO 10126
      pkcs7: PKCS#7
      zero: Zero Padding
    plaintext: Plaintext
    privateKey: Private Key
    privateKeyPlaceholder: Enter private key...
    publicKey: Public Key
    publicKeyPlaceholder: Enter public key...
    show: Show
    success:
      copied: Result copied to clipboard
      keyPairGenerated: Key pair generated successfully
    symmetric: Symmetric
    title: Encryption/Decryption
    useIv: Use Initialization Vector
  history:
    description: Your recently used tools
    inputPrefix: "Input:"
    title: Usage History
  jsonFormatter:
    compactMode: Compact Mode
    copiedToClipboard: Copied to clipboard
    copy: Copy
    description: Format, validate and beautify JSON data
    download: Download
    escapeHtml: Escape HTML
    format: Format
    formattedOutputPlaceholder: Formatted JSON will appear here
    indentSpaces: Indent Spaces
    inputJson: Input JSON
    invalidJsonFormat: Invalid JSON format
    jsonMinify: JSON Minify
    minify: Minify
    outputJson: Output JSON
    pasteJsonHere: Paste JSON data here...
    pleaseEnterJsonData: Please enter JSON data
    sortKeys: Sort Keys
    title: JSON Formatter
    upload: Upload File
  jwtDecoder:
    decodeButton: Decode
    description: Decode and inspect JWT tokens
    errors:
      decodeFailed: Decode failed
      inputRequired: Input is required
      invalidFormat: Invalid JWT format
    header: Header
    inputLabel: JWT Token
    inputPlaceholder: Paste your JWT token here...
    payload: Payload
    success:
      copied: Copied to clipboard
    title: JWT Decoder
  nameGenerator:
    count: Count
    description: Generate random names from different cultures
    gender: Gender
    genders:
      female: Female
      male: Male
      random: Random
    generate: Generate
    generatedNames: Generated Names
    includeMiddleName: Include Middle Name
    nationalities:
      american: American
      arabic: Arabic
      british: British
      chinese: Chinese
      french: French
      german: German
      indian: Indian
      japanese: Japanese
      random: Random
      russian: Russian
      spanish: Spanish
    nationality: Nationality
    noNamesGenerated: No names generated yet
    title: Name Generator
    errors:
      generateFailed: Failed to generate names
  noToolsFound: No tools found
  normalMode: Normal Mode
  passwordGenerator:
    customCharSet: Custom Character Set
    customCharSetPlaceholder: Enter custom characters...
    description: Generate secure passwords with customizable options
    errors:
      charSetEmpty: Character set cannot be empty
      customCharSetRequired: Character set cannot be empty when using custom character set
      generateFailed: Failed to generate password
    excludeAmbiguous: Exclude Ambiguous Characters
    excludeSimilar: Exclude Similar Characters
    generatePassword: Generate Password
    includeLowercase: Include Lowercase Letters
    includeNumbers: Include Numbers
    includeSpecialChars: Include Special Characters
    includeUppercase: Include Uppercase Letters
    noPasswordHistory: No password history
    passwordHistory: Password History
    passwordLength: Password Length
    passwordStrength: Password Strength
    strength:
      extremelyStrong: Extremely Strong
      medium: Medium
      strong: Strong
      veryStrong: Very Strong
      veryWeak: Very Weak
      weak: Weak
    success:
      copied: Password copied to clipboard
    title: Password Generator
    useCustomCharSet: Use Custom Character Set
  searchPlaceholder: Search tools...
  searchResults: "Found {{count}} tools matching '{{query}}'"
  textUtils:
    caseConverter: Case Converter
    charsLabel: Characters
    copyButton: Copy
    description: Powerful text processing tools
    encoding: Encoding
    errors:
      base64DecodeFailed: Base64 decode failed
      base64EncodeFailed: Base64 encode failed
      processError: Process error
      uriDecodeFailed: URI decode failed
    inputPlaceholder: Enter text to process...
    inputTextLabel: Input Text
    linesLabel: Lines
    operations:
      base64Decode: Base64 Decode
      base64Encode: Base64 Encode
      capitalize: Capitalize
      htmlDecode: HTML Decode
      htmlEncode: HTML Encode
      lowercase: Lowercase
      normalizeSpaces: Normalize Spaces
      removeEmptyLines: Remove Empty Lines
      removeSpaces: Remove Spaces
      reverse: Reverse
      reverseSort: Reverse Sort
      shuffle: Shuffle
      sort: Sort
      spacesToTabs: Spaces to Tabs
      tabsToSpaces: Tabs to Spaces
      titleCase: Title Case
      trimLines: Trim Lines
      uniqueLines: Unique Lines
      uppercase: Uppercase
      uriDecode: URI Decode
      uriEncode: URI Encode
    outputPlaceholder: Processed text will appear here...
    outputTextLabel: Output Text
    processButton: Process
    textOperations: Text Operations
    title: Text Utils
    whitespace: Whitespace
    wordsLabel: Words
  title: Online Tools
  tools:
    base64: Base64 Encoder
    base64Desc: Base64 encoding and decoding
    color: Color Picker
    colorDesc: Convert between different color formats
    efficientOnlineTools: Efficient Online Tools Collection
    efficientOnlineToolsDescription: Curated collection of practical online tools to boost productivity, no installation required, ready to use
    encryption: Encryption/Decryption
    encryptionDesc: Encrypt and decrypt text using various algorithms
    exploreAllTools: Explore All Tools
    hash: Hash Generator
    hashDesc: Generate hash values for text
    json: JSON Formatter
    jsonDesc: Format, validate and beautify JSON data
    jwt: JWT Tool
    jwtDesc: Decode and inspect JWT token contents
    loginToSaveTools: Login to save your favorite tools
    name: Random Name
    nameDesc: Generate random names
    onlineTools: Online Tools
    password: Password Generator
    passwordDesc: Generate secure random passwords
    saveMyTools: Save My Tools
    text: Text Processing
    textDesc: Text conversion and processing tools
    url: URL Encoder
    urlDesc: URL encoding and decoding
    uuid: UUID Generator
    uuidDesc: Generate UUIDs of various versions
    yamlProperties: YAML/Properties Converter
    yamlPropertiesDesc: Convert between YAML and Properties file formats
    yamlPropertiesConverter:
      title: YAML/Properties Converter
      description: Convert between YAML and Properties file formats
      errors:
        inputRequired: Input required
        conversionFailed: Conversion failed
        copyFailed: Copy failed
      success:
        converted: Conversion successful
      yamlInput: YAML Input
      propertiesInput: Properties Input
      yamlOutput: YAML Output
      propertiesOutput: Properties Output
      enterYamlContent: Enter YAML content...
      enterPropertiesContent: Enter Properties content...
      convertedResultHere: Converted result will appear here...
      copy: Copy
      copied: Copied
      convert: Convert
      clear: Clear
  toolsCount: tools
  toolsList: Tools List
  tryDifferentSearch: Try different search keywords
  urlEncoder:
    component: URI Component
    decode: Decode
    decodeOutput: Decode Output
    decodeOutputPlaceholder: Decoded result will appear here...
    description: URL encoding and decoding tool
    encode: Encode
    encodeAll: Encode All Characters
    errors:
      decodeFailed: Decode failed
      processError: Processing error
    inputPlaceholder: Enter text to encode...
    inputText: Input Text
    inputUrl: Input URL
    inputUrlPlaceholder: Enter URL to decode...
    mode: Mode
    output: Output
    outputPlaceholder: Encoded result will appear here...
    plusForSpace: Plus for Space
    standard: Standard URI
    title: URL Encoder
  uuid:
    copyAll: Copy All
    count: Count
    countLabel: Count
    description: Generate UUIDs of various versions
    errors:
      generateError: Generate error
      v3Required: Namespace and name required for v3
      v5Required: Namespace and name required for v5
    generate: Generate
    generated: Generated UUIDs
    name: Name
    namePlaceholder: Enter name...
    namespace: Namespace
    namespacePlaceholder: Enter namespace UUID...
    title: UUID Generator
    version: Version
orPermanent: or create permanent link
ourMission: Our Mission
ourMissionDesc: Building better digital experiences
privacyFocus: Privacy Focus
privacyFocusDesc: Your data security is our priority
profile:
  account: Account
  accountDescription: Manage your profile information and avatar
  avatarError: Avatar operation failed
  avatarRemoved: Avatar Removed
  avatarRemovedDesc: Your avatar has been successfully removed
  avatarUpdated: Avatar Updated
  avatarUpdatedDesc: Your avatar has been successfully updated
  changePicture: Change Picture
  confirmNewPassword: Confirm New Password
  confirmNewPasswordPlaceholder: Enter new password again
  connect: Connect
  connected: Connected
  connections: Connections
  currentPassword: Current Password
  disconnect: Disconnect
  disconnectError: Failed to disconnect
  disconnected: Disconnected
  disconnectedDesc: Third-party account connection has been disconnected
  emailAddress: Email Address
  emailNotVerified: Email Not Verified
  emailNotVerifiedAlert: Your email address is not verified. Please check your inbox and click the verification link.
  emailSendError: Failed to send email
  emailSent: Email Sent
  emailVerified: Email Verified
  enterCurrentPassword: Enter current password
  enterNewPassword: Enter new password
  error: Error
  imageSizeLimit: Image size cannot exceed 5MB
  manageConnections: Manage your account settings and connected services
  memberSince: Member Since
  newPassword: New Password
  noOAuthProviders: No OAuth providers available
  notConnected: Not Connected
  password: Password
  passwordUpdateError: Failed to update password
  passwordUpdated: Password Updated
  passwordUpdatedDesc: Your password has been successfully updated
  primary: Primary
  remove: Remove
  selectImage: Select Image
  selectImageFile: Please select an image file
  sendVerificationEmail: Send Verification Email
  settings: Settings
  subtitle: Manage your account settings and preferences
  superAdmin: Super Admin
  title: Profile
  unverified: Unverified
  updatePassword: Update Password
  uploadImageDesc: Supports JPG, PNG formats, max 5MB
  uploadProfilePicture: Upload Profile Picture
  verificationEmailSentDesc: Verification email has been sent to your email
  verified: Verified
quickStart: Quick Start
quickStartDesc: Get started with our tools in just a few clicks
receiveMessages: Receive messages safely
registeredUsers: Registered Users
stats:
  clickForDetails: Click for details
  hotValue: "Hot Value: "
  newsCount: "News Count: "
  platformDistributionAndRankings: Platform distribution and hot news rankings
  todaysHotListAnalytics: Todays Hot List Analytics
  visits: Visits
status:
  backend: Go Backend
  checking: Checking...
  connected: Connected
  connectionCheckFailed: Connection check failed
  connectionFailed: Connection Failed
  status: Status
  unknown: Unknown
systemSettings: System Settings
tempEmail: Temporary emails
tempEmailOptions: Temporary Email Options
todoMemo:
  addMemo: Add Memo
  addTodo: Add Todo
  backToList: Back to List
  clientPresentation: Client Presentation
  completed: Completed
  contentDisplayError: Content display error
  created: Created
  createdOn: "Created on: "
  delete: Delete
  description: Organize your tasks and notes
  dueDate: Due Date
  dueDates: Due Dates
  dueDatesDesc: Set and manage deadlines for your tasks
  edit: Edit
  expand: Expand
  goToLatestMemo: Go to latest memo
  high: High
  imageAlt: "Image {{index}}"
  images: Images
  latest: Latest
  loading: Loading...
  login: Login
  loginRequired: You need to be logged in to access this page.
  low: Allowed Domains
  medium: Medium
  memo:
    add: Add Category
    addTag: Add tag
    messages:
      addFailed: Failed to add memo
      added: Memo added successfully
      deleteFailed: Failed to delete memo
      deleted: Memo deleted successfully
      loadFailed: Failed to load memos
      loginRequired: You need to be logged in to manage memos
      updateFailed: Failed to update memo
      updated: Memo updated successfully
    clearDate: Clear Date
    clickNewMemoToCreate: Click 'New Memo' to create your first memo
    close: Close
    content: Content
    details: Details
    editMemo: Edit Memo
    edited: edited
    enterMemoContent: Enter memo content
    memos: Memos
    newMemo: New Memo
    next: Next memo
    noMemos: No memos yet
    noMemosOnPage: No memos on this page
    pageOf: "Page {{current}} of {{total}}"
    pickReminderDate: Pick reminder date
    previous: Previous memo
    reminderDate: Reminder Date
    tags: Tags
    timeAgo:
      dayAgo: a day ago
      daysAgo: "{{count}} days ago"
      hourAgo: an hour ago
      hoursAgo: "{{count}} hours ago"
      justNow: just now
      minuteAgo: a minute ago
      minutesAgo: "{{count}} minutes ago"
  memoDetails: Memo Details
  memos: Memos
  minimize: Minimize
  myTasks: My Tasks
  nextMemo: Next memo
  noActiveTodos: No active todos
  noContent: No content
  noMemosYet: No memos yet
  pending: Pending Approval
  previousMemo: Previous memo
  priority: Priority
  projectProposal: Project Proposal
  quickNote: Quick Notes
  quickNotes: Quick Notes
  quickNotesDesc: Capture ideas and thoughts instantly
  reminder: Reminder
  reminders: Reminders
  remindersDesc: Get notified about important tasks and events
  sampleNote: This is a sample note to demonstrate the memo feature
  status: Status
  tags: Tags
  taskManagement: Task Management
  taskManagementDesc: Organize and track your tasks efficiently
  teamMeeting: Team Meeting
  title: "Todo & Memo"
  todo:
    messages:
      addFailed: Failed to add todo
      added: Todo added successfully
      deleteFailed: Failed to delete todo
      deleted: Todo deleted successfully
      loadFailed: Failed to load todos
      loginRequired: You need to be logged in to manage todos
      updateFailed: Failed to update todo
      updated: Todo updated successfully
  todos: Todos
  viewAll: View All
tools:
  urlEncoder:
    errors:
      inputRequired: Input is required
      decodeFailed: Failed to decode
      processError: Process error
    success:
      copied: Copied to clipboard
    component: Component
    decode: Decode
    decodeOutput: Decode Output
    decodeOutputPlaceholder: Decoded output will appear here...
    description: Encode and decode URLs and components
    encode: Encode
    encodeAll: Encode All
    inputPlaceholder: Paste your JWT token here...
    inputText: Input Text
    inputUrl: Input URL
    inputUrlPlaceholder: Enter URL to encode/decode...
    mode: Mode
    output: Output
    outputPlaceholder: Encoded output will appear here...
    plusForSpace: Plus for Space
    standard: Standard
    title: URL Encoder/Decoder

  colorConverter:
    colorName: Color Name
    colorPicker: Color Picker
    convert: Convert
    copied: Copied
    description: Convert between different color formats
    format: Format
    inputColor: Input Color
    title: Color Converter
    value: Value
  efficientOnlineTools: Efficient Online Tools Collection
  efficientOnlineToolsDescription: Curated collection of practical online tools to boost productivity, no installation required, ready to use
  exploreAllTools: Explore All Tools
  history:
    description: Recently used tools and inputs
    inputPrefix: "Input: "
    noHistory: No history available
    title: History
  jsonFormatter:
    compactMode: Compact Mode
    copiedToClipboard: Copied to clipboard
    copy: Copy
    description: Beautify or minify JSON data for better readability and analysis
    download: Download
    escapeHtml: Escape HTML
    format: Format
    formattedOutputPlaceholder: Formatted output will appear here...
    indentSpaces: Indent Spaces
    inputJson: Input JSON
    invalidJsonFormat: Invalid JSON format
    jsonMinify: JSON Minify
    minify: Minify
    outputJson: Output JSON
    pasteJsonHere: Paste JSON data here...
    pleaseEnterJsonData: Please enter JSON data
    sortKeys: Sort Keys
    title: JSON Formatter
    upload: Upload
  jwtDecoder:
    decodeButton: Decode
    description: Decode and inspect JWT tokens
    errors:
      invalidFormat: Invalid JWT format
    header: Header
    inputLabel: JWT Token
    inputPlaceholder: Paste your JWT token here...
    payload: Payload
    title: JWT Decoder
  loginToSaveTools: Login to save your favorite tools
  nameGenerator:
    count: Count
    description: Generate random names from different cultures
    gender: Gender
    genders:
      female: Female
      male: Male
      random: Random
    generate: Generate
    generatedNames: Generated Names
    includeMiddleName: Include Middle Name
    nationalities:
      random: Random
    nationality: Nationality
    noNamesGenerated: No names generated yet
    title: Name Generator
  onlineTools: Online Tools
  passwordGenerator:
    customCharSet: Custom Character Set
    customCharSetPlaceholder: Enter custom characters...
    description: Generate secure passwords with customizable options
    excludeAmbiguous: Exclude Ambiguous Characters
    excludeSimilar: Exclude Similar Characters
    generatePassword: Generate Password
    includeLowercase: Include Lowercase Letters
    includeNumbers: Include Numbers
    includeSpecialChars: Include Special Characters
    includeUppercase: Include Uppercase Letters
    noPasswordHistory: No password history
    passwordHistory: Password History
    passwordLength: Password Length
    passwordStrength: Password Strength
    strength:
      extremelyStrong: Extremely Strong
      medium: Medium
      strong: Strong
      veryStrong: Very Strong
      veryWeak: Very Weak
      weak: Weak
    title: Password Generator
    useCustomCharSet: Use Custom Character Set
  saveMyTools: Save My Tools
  textUtils:
    caseConverter: Case Converter
    charsLabel: Characters
    copyButton: Copy
    description: Powerful text processing tools
    encoding: Encoding
    errors:
      base64DecodeFailed: Base64 decode failed
      base64EncodeFailed: Base64 encode failed
      processError: Process error
      uriDecodeFailed: URI decode failed
    inputPlaceholder: Paste your JWT token here...
    inputTextLabel: Input Text
    linesLabel: Lines
    operations:
      base64Decode: Base64 Decode
      base64Encode: Base64 Encode
      capitalize: Capitalize
      htmlDecode: HTML Decode
      htmlEncode: HTML Encode
      lowercase: Lowercase
      normalizeSpaces: Normalize Spaces
      removeEmptyLines: Remove Empty Lines
      removeSpaces: Remove Spaces
      reverse: Reverse
      reverseSort: Reverse Sort
      shuffle: Shuffle
      sort: Sort
      spacesToTabs: Spaces to Tabs
      tabsToSpaces: Tabs to Spaces
      titleCase: Title Case
      trimLines: Trim Lines
      uniqueLines: Unique Lines
      uppercase: Uppercase
      uriDecode: URI Decode
      uriEncode: URI Encode
    outputPlaceholder: Processed text will appear here...
    outputTextLabel: Output Text
    processButton: Process
    textOperations: Text Operations
    title: Text Utils
    whitespace: Whitespace
    wordsLabel: Words
  uuid:
    copyAll: Copy All
    count: Count
    countLabel: Count
    description: Generate UUIDs of various versions
    errors:
      generateError: Generate error
      v3Required: Namespace and name required for v3
      v5Required: Namespace and name required for v5
    generate: Generate
    generated: Generated UUIDs
    name: Name
    namePlaceholder: Enter name...
    namespace: Namespace
    namespacePlaceholder: Enter namespace UUID...
    title: UUID Generator
    version: Version
tryItNow: Try It Now
unregisteredUsers: Unregistered Users
upTo30Days: Valid for up to 30 days
url:
  domainNotInWhitelist: Domain not in whitelist
  expiration: Expiration
  goToHomepage: Go to Homepage
  invalidLinkDescription: The link you're looking for doesn't exist or has expired
  invalidShortUrl: Invalid short URL
  never: Never
  oneDay: 1 Day
  oneHour: 1 Hour
  oneMonth: 1 Month
  oneWeek: 1 Week
  redirecting: Redirecting...
  shortUrlNotFound: Short URL not found
  shortenAUrl: Shorten a URL
  shortenUrl: Shorten URL
  shortenUrlDescription: Convert long URLs into short, easy-to-share links
  urlExpired: URL has expired
  urlPlaceholder: Enter URL to shorten
urlExpiration: URL Expiration Options
urlExpirationDesc: Choose how long your short URLs remain active
urlExpirationOption1: 1 hour - Quick temporary sharing
urlExpirationOption2: 24 hours - Daily use links
urlExpirationOption3: 7 days - Weekly projects
urlExpirationOption4: 30 days - Monthly campaigns
urlExpirationOption5: Never expire - Permanent links
urlFeature1Desc: Convert long URLs into short, memorable links
urlFeature1Title: Quick URL Shortening
urlFeature2Desc: Create personalized short links with custom codes
urlFeature2Title: Custom Short Codes
urlFeature3Desc: Track clicks and analyze link performance
urlFeature3Title: Click Analytics
urlFeature4Desc: Automatically generate QR codes for your links
urlFeature4Title: QR Code Generation
urlFeature5Desc: Process multiple URLs at once
urlFeature5Title: Bulk URL Processing
urlFeature6Desc: Integrate with your applications using our API
urlFeature6Title: API Access
urlShortener:
  allowedDomains: Allowed Domains
  description: Create short, easy-to-share links
  domains: Domains
  manage: Manage
  more: More
  readyToShorten: Ready to Shorten
  shortenUrl: Shorten URL
  tabs:
    domainWhitelist: Domain Whitelist
    domains: Domains
    shortenUrls: Shorten URLs
    urls: URLs
  title: URL Shortener
userManagement: User Management
view: "View"
