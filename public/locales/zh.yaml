about:
  aboutProject: 项目介绍
  aboutUs: 关于我们
  communityBuilt: 社区共建
  communityDriven: 社区驱动
  communityDrivenDesc: 由开发者社区共同构建和维护
  communityOriented: 社区导向 - 倾听用户声音，持续改进
  contribute: 贡献代码
  description: 您的一站式生产力平台，提供URL缩短、临时邮箱等服务。
  faq: 常见问题
  features:
    hotList:
      categorizedContent: 分类热门话题
      checkNow: 立即查看
      description: 聚合各大平台热门内容，一站式获取最新资讯和趋势。
      multiplePlatforms: 多平台内容聚合
      realTimeUpdates: 实时热门内容更新
      title: 今日热榜
    navigation:
      categorizedWebsites: 分类整理的优质网站
      description: 精心整理的网站分类，发现优质资源，提升工作效率。
      exploreNow: 立即探索
      publicPrivate: 支持公开和私人收藏
      submitResources: 提交优质资源
      title: 网站导航
    notes:
      description: 使用我们的笔记系统捕捉想法并整理思路
      organizeByCategories: 按类别组织
      quickCapture: 快速捕捉想法
      searchFunctionality: 强大的搜索功能
      title: 快速笔记和备忘录
      tryNow: 立即试用
    tempEmail:
      autoExpiration: 自动过期清理
      description: 快速生成临时邮箱地址，保护隐私，避免垃圾邮件。
      instantCreation: 即时创建邮箱
      noRegistration: 无需注册
      title: 临时邮箱
      tryNow: 立即试用
    todoList:
      description: 使用我们直观的待办管理器组织任务并提升效率
      dueDateReminders: 截止日期提醒
      progressTracking: 进度跟踪
      taskPrioritization: 任务优先级系统
      title: 待办任务管理
      tryNow: 立即试用
    urlShortening:
      analytics: 详细访问统计
      customCodes: 自定义短链代码
      description: 将长网址转换为短小易记的链接，支持自定义代码和详细分析。
      qrGeneration: 自动生成二维码
      title: URL短链服务
      tryNow: 立即试用
  forever: 永久免费
  getStartedFree: 免费开始使用
  joinUs: 加入我们
  joinUsDesc: 成为我们不断发展的社区的一份子
  learnMore: 了解更多
  license: 许可证
  mission: 我们的使命
  missionDescription: 我们致力于提供简单、安全、高效的在线工具，让您的数字生活更加便利。我们相信技术应该为人服务，而不是相反。
  openSource: 开源项目
  openSourceDesc: 完全开源，透明可靠
  openSourceProject: 开源项目
  openSourceProjectDesc: 完全开源的一站式工具平台，社区驱动，永久免费
  ourFeatures: 我们的功能
  ourValues: 我们的价值观
  overview:
    description: 探索我们的综合在线工具套件，旨在让您的数字生活更高效、更便利。
    hotList:
      description: 获取来自各个平台的最新热门内容
      title: 实时热榜聚合
    navigation:
      description: 发现并收藏优质网络资源
      title: 精选网站导航
    signUp: 立即注册
    tempEmail:
      description: 保护隐私的一次性邮箱解决方案
      title: 临时邮箱服务
    title: 功能概览
    urlShortening:
      description: 快速生成短链接，支持自定义和分析
      title: 智能URL缩短
  privacyFirst: 隐私优先 - 您的数据安全是我们的首要任务
  privacyFirstDesc: 保护您的数据隐私和安全
  privacyFocus: 隐私保护
  privacyFocusDesc: 您的数据安全是我们的优先考虑
  questions:
    contribute: 我可以为这个项目做贡献吗？
    contributeAnswer: 绝对可以！我们欢迎社区贡献。您可以在我们的GitHub仓库提交问题、建议或代码贡献。
    dataPrivacy: 我的数据安全吗？
    dataPrivacyAnswer: 绝对安全。我们使用行业标准加密来保护您的数据，绝不会与第三方共享您的个人信息。
    freeServices: 这些服务是免费的吗？
    freeServicesAnswer: 是的，我们所有的核心服务都是完全免费的。我们致力于提供高质量的工具而不收取任何费用。
    newFeatures: 你们会添加新功能吗？
    newFeaturesAnswer: 是的，我们会根据用户反馈和需求持续改进并添加新功能。请关注我们的更新公告。
    urlShutdown: 如果服务关闭，我的短链接会怎样？
    urlShutdownAnswer: 我们承诺在任何服务变更前提前通知用户，并提供数据导出选项，确保您不会丢失重要链接。
  signUp: 立即注册
  simplicityEase: 简单易用 - 直观界面，无需学习成本
  sourceCode: 查看源代码
  transparency: 透明开放 - 开源代码，透明运营
  viewSource: 查看源码
  whyChooseUs:
    constantlyEvolving:
      description: 我们不断改进并添加新功能，为用户提供更好的体验和更实用的工具。
      title: 持续进化
    fastReliable:
      description: 高性能服务器和优化代码确保快速响应和稳定的服务体验。
      title: 快速可靠
    privacyFocused:
      description: 我们重视您的隐私，所有数据都经过加密，确保您的信息安全。
      title: 注重隐私
    title: 为什么选择我们
admin:
  addDomain: 添加域名
  addDomainToWhitelist: 添加域名到白名单
  addFeature: 添加功能
  addNewFeature: 添加新功能
  badgeChinese: 徽章（中文）
  badgeEnglish: 徽章（英文）
  badgeExampleEn: 例如：NEW
  badgeExampleZh: 例如：新功能
  banner:
    addLine: 添加行
    advancedConfiguration: 高级配置
    animationSpeed: 动画速度
    bannerHeight: 横幅高度（像素）
    basicConfiguration: 基础配置
    default: 一站式工具集
    defaultTexts:
      allInOneToolbox: 一站式工具集
      boostProductivity: 提升您的生产力
      easyToUse: 易于使用
      professionalReliable: 专业可靠
    displayStyle: 显示样式
    dynamicTextLines: 动态文本行
    enterText: 输入要哈希的文本
    firstLine: 第一行
    flow: 流动
    fourthLine: 第四行
    gradient: 渐变
    inline: 内联
    lineSpacing: 行间距
    loadConfigFailed: 加载配置失败
    noDynamicLines: 未添加动态行。将从基础配置生成行。
    preview: 预览
    saveConfigSuccess: 配置保存成功
    saveConfiguration: 保存配置
    secondLine: 第二行
    stacked: 堆叠
    styleOptions: 样式选项
    tailwindGradient: Tailwind渐变
    text: 文本
    thirdLine: 第三行
    togglePreview: 切换预览
    useSecondLineBreak: 使用第二行换行
  bannerConfiguration: 横幅配置
  bannerConfigurationDescription: 自定义主页横幅中显示的功能和主标题。
  bannerFeatures: 横幅功能
  bannerFeaturesDescription: 配置主页横幅中显示的功能卡片
  charts:
    uniqueVisitors: 独立访客
    visits: 访问量
  customizeFeatureDescription: 自定义此功能在网站上的显示方式。
  descriptionChinese: 描述（中文）
  descriptionEnglish: 描述（英文）
  domainReviewNotification: 域名将由管理员审核
  domainWhitelist:
    addDomain: 添加域名
    addNewDomain: 添加新域名
    allDomains: 所有域名
    approve: 批准
    approvedDomains: 已批准域名
    confirmDelete: 您确定要删除这个域名吗？
    created: 创建时间
    description: 管理允许的域名列表
    domainsAwaitingApproval: 等待审核的域名
    enterDomainPlaceholder: 输入域名（例如：example.com）
    noDomainsFound: 未找到域名
    paginationInfo: "第{{current}}页，共{{total}}页"
    pendingApproval: 等待审核
    refresh: 刷新
    reject: 拒绝
    reviewAndApprove: 审核并批准用户提交的链接
    searchDomainsPlaceholder: 搜索域名...
    showingItems: "显示第{{start}}到{{end}}项，共{{total}}个域名"
    title: 域名白名单管理器
  domains:
    addedSuccessfully: 域名添加成功
    deletedSuccessfully: 域名删除成功
    failedToAdd: 添加域名失败
    failedToDelete: 删除链接失败
    failedToLoadWhitelist: 加载域名白名单失败
    failedToUpdateStatus: 更新域名状态失败
    statusUpdatedSuccessfully: 域名状态更新成功
  editFeature: 编辑功能
  emailDomains:
    actions: 操作
    addDomain: 添加域名
    addFailed: 添加域名失败
    addSuccess: 域名添加成功
    deleteFailed: 删除域名失败
    deleteSuccess: 域名删除成功
    description: 管理临时邮箱服务的允许邮箱域名
    domain: 域名
    domainAlreadyExists: 域名已存在
    enterDomain: 输入域名（例如：example.com）
    enterDomainName: 请输入域名
    loadFailed: 加载域名失败
    noDomainsFound: 未找到域名
    title: 邮箱域名管理
  enterDomainForApproval: 输入要审核的域名
  featureGridLayout: 功能网格布局
  featureManagement: 功能管理
  features:
    errors:
      mustLoginToSave: 保存配置前必须登录
      saveConfigFailed: 保存配置失败
    featureDeleted: 功能已删除。记得保存您的更改。
    loadBannerTitleFailed: 加载横幅标题配置失败
    loadConfigFailed: 加载配置失败
    newFeature:
      description: 功能描述
      title: 新功能
    orderUpdated: 顺序已更新。记得保存您的更改。
    saveConfigFailed: 保存配置失败
    saveConfigSuccess: 配置保存成功
  featuresPerRow: 每行功能数
  fiveFeatures: 5个功能
  fourFeatures: 4个功能
  gridLayoutDescription: 配置功能卡片在主页横幅中的显示方式。1行6个功能时，所有卡片将显示在一行中。
  icon: 图标
  iconBackground: 图标背景
  link:
    down: 下移
    failedToFetchLinks: 获取链接失败
    failedToMoveLink: 移动链接失败
    nameUrlCategoryRequired: 名称、URL和分类是必需的
    up: 上移
  linkModeration:
    approve: 已批准
    approved: 已批准
    category: 分类名称
    error: 错误
    failedToApproveLink: 批准链接失败
    failedToLoadSubmissions: 加载提交失败
    failedToRejectLink: 拒绝链接失败
    linkApproved: 链接已批准
    linkApprovedDesc: 链接已成功批准
    linkRejected: 链接已拒绝
    linkRejectedDesc: 链接已成功拒绝
    loadingSubmissions: 正在加载提交...
    noPendingSubmissions: 没有待审核的提交
    pending: 等待审核
    pendingLinkSubmissions: 待审核链接提交
    recentlyModerated: 最近审核
    reject: 已拒绝
    rejected: 已拒绝
    submittedBy: 提交者：
    submittedOn: 提交时间：
  mainTitleAndDescription: 主标题和描述
  mainTitleChinese: 主标题（中文）
  mainTitleEnglish: 主标题（英文）
  map:
    visitorMap: 访客地图
    visitorMapDescription: 查看访客的地理分布
  navigationManager:
    categories: 分类
    description: 管理导航分类和链接
    links: 链接
    moderation: 审核
    preview: 预览
    title: 导航管理器
  navigationPreview:
    doneReordering: 完成重新排序
    dragAndDropToReorder: 拖拽重新排序链接
    external: "{{name}} - 外部工具"
    failed: 加载邮件失败
    internal: "{{name}} - 内部工具"
    loadingPreview: 正在加载预览...
    noCategoriesFound: 未找到分类
    noLinksInCategory: 此分类中没有可用的链接。添加您的第一个链接！
    ping: 延迟
    reorderLinks: 重新排序链接
    title: 导航预览
  numberOfRows: 行数
  oneRow: 1行
  optional: （可选）
  panel:
    description: 系统管理和配置中心
    refresh: 刷新
    title: 管理面板
  s3Config:
    addConfig: 添加S3配置
    bucket: 存储桶名称
    bucketName: 存储桶名称
    configDescription: 配置文件上传的S3存储设置
    configName: 配置名称
    confirmDelete: 确认删除
    confirmDeleteDescription: 您确定要删除这个S3配置吗？此操作无法撤销。
    createSuccess: S3配置创建成功
    customEndpoint: 自定义端点（可选）
    deleteFailed: 删除S3配置失败
    deleteSuccess: S3配置删除成功
    description: 配置文件上传的S3存储设置
    editConfig: 编辑S3配置
    fetchFailed: 获取S3配置失败
    keepUnchanged: 留空以保持不变
    noConfigsFound: 未找到S3配置
    region: 地区
    saveFailed: 保存S3配置失败
    setAsDefault: 设为默认
    setDefaultFailed: 设置默认S3配置失败
    setDefaultSuccess: 默认S3配置设置成功
    testFailed: S3配置测试失败
    testSuccess: S3配置测试成功
    title: S3配置
    updateSuccess: S3配置更新成功
    useSSL: 使用SSL
  saveChanges: 保存更改
  saveConfiguration: 保存配置
  section: 区域
  selectColumns: 选择列数
  selectGradient: 选择渐变
  selectIcon: 选择图标
  selectRows: 选择行数
  selectSection: 选择区域
  settings:
    defaultUrlExpiration: 默认URL过期时间（天）
    description: 管理系统设置和配置
    domainForShortUrls: 短链接域名
    saveSettings: 保存设置
    shortUrlsAlwaysUseCurrentDomain: 短链接将始终使用当前访问的域名
    systemDefault: 系统默认
    urlSettings: URL设置
  show: 显示
  showingDomainsCount: "显示{{filtered}}个域名，共{{total}}个"
  showingUsersInfo: "显示 {{current}}/{{total}} 个用户"
  sixFeatures: 6个功能
  stats:
    categories:
      designResources: 设计资源
      developmentTools: 开发工具
    domainTraffic: 域名流量
    shortUrlVisits: 短链接访问
    statistics: 统计信息
    systemUsageAndAnalytics: 系统使用情况和分析
    timeline: 时间线
    visitorDistribution: 访客分布
    visitorDistributionDescription: 网站访客的地理分布
  submitDomain: 提交域名
  submitDomainNotification: 域名将在批准前由管理员审核
  threeFeatures: 3个功能
  threeRows: 3行
  titleChinese: 标题（中文）
  titleEnglish: 标题（英文）
  twoFeatures: 2个功能
  twoRows: 2行
auth:
  accountLink: 账户链接
  accountLinkFailed: 账户链接失败
  accountLinkResponseError: 账户链接响应格式错误
  accountLinkSuccess: 账户链接成功
  alreadyHaveAccount: 已有账户？
  avatar: 头像
  backToLogin: 返回登录页面
  backToRegister: 返回注册页面
  checkEmailForResetLink: 请检查您的邮箱以获取重置链接
  confirmNewPassword: 确认新密码
  confirmPassword: 确认密码
  createAccount: 创建账户
  createNewAccount: 创建新账户
  dontHaveAccount: 没有账户？
  email: 邮箱
  emailNotVerifiedMessage: 您的邮箱地址未验证。请检查您的邮箱并点击验证链接。
  emailNotVerifiedPattern: 邮箱未验证
  emailSent: 邮件已发送
  emailVerification: 邮箱验证
  emailVerificationSuccess: 邮箱验证成功！您的账户已创建。
  enterNewPasswordInstructions: 请在下方输入您的新密码
  enterUsername: 输入用户名
  expiredResetToken: 重置令牌已过期
  failedToLoadOAuthProviders: 加载OAuth提供商失败
  failedToSendResetEmail: 发送重置邮件失败
  failedToSendVerificationEmail: 发送验证邮件失败
  failedToSendVerificationEmailFallback: 发送验证邮件失败
  failedToStartOAuth: 启动OAuth登录失败
  forgotPassword: 忘记密码？
  forgotPasswordDescription: 输入您的邮箱地址，我们将发送重置密码的链接
  forgotPasswordTitle: 忘记密码
  invalidToken: 收到无效的认证令牌
  invalidVerificationLink: 无效的验证链接
  linkAccount: 链接账户
  linkedToExistingAccount: 已链接到现有账户
  loading: 加载中...
  loadingDots: 加载中...
  loginDescription: 输入您的邮箱和密码登录
  loginFailed: 登录失败，请检查您的凭据。
  loginRequired: 需要登录
  loginResponseError: 登录响应格式错误
  loginSuccess: 登录成功！
  loginSuccessful: 您已成功登录！
  loginTitle: 登录
  loginWith: "使用 {{provider}} 登录"
  missingAccountLinkToken: 缺少账户链接令牌
  missingAuthCode: 缺少授权码
  missingProviderInfo: 缺少提供商信息
  newAccountCreated: 新账户创建成功
  newPassword: 新密码
  oauthLoginFailed: OAuth登录失败
  orContinueWithEmail: 或使用邮箱继续
  password: 密码
  passwordMinLength: 密码必须至少8个字符
  passwordResetEmailSent: 密码重置邮件已发送
  passwordUpdateFailed: 密码更新失败
  passwordUpdateSuccess: 密码更新成功
  passwordsDoNotMatch: 密码不匹配
  passwordsDontMatch: 密码不匹配
  pleaseEnterUsername: 请输入用户名
  processingLogin: 正在处理登录...
  redirecting: 正在重定向...
  registerDescription: 创建新账户开始使用
  registerFailed: 注册失败。请重试。
  registerSuccess: 注册成功
  registerTitle: 注册
  registrationFailed: 注册失败，请重试
  rememberPassword: 记起密码了？
  resendIn: 重新发送于
  resendVerificationEmail: 重新发送验证邮件
  resetEmailSent: 重置邮件已发送
  resetEmailSentDesc: 密码重置邮件已发送到您的邮箱地址
  resetPasswordDescription: 输入您的邮箱地址以接收密码重置链接
  resetPasswordTitle: 重置密码
  sendResetEmail: 发送重置邮件
  sendingDots: 发送中...
  setUsernameForNewAccount: 请设置用户名以创建新账户
  stateVerificationFailed: 状态验证失败，请重新登录
  updateYourPassword: 更新您的密码
  username: 用户名
  verificationEmailSent: 验证邮件已发送到您的邮箱地址。请检查收件箱并点击验证链接。
  verificationExpired: 验证失败，链接可能已过期
  verificationFailed: 验证失败，请重试
  verificationSuccess: 验证成功
  verifyResetTokenFailed: 验证重置令牌失败
  verifying: 正在验证您的邮箱...
  verifyingEmail: 正在验证您的邮箱...
  welcomeBack: 欢迎回来！
chooseExpiration: 选择过期时间
common:
  actions: 操作
  actionsLabel: 操作
  active: 活跃
  activeShortUrls: 活跃短链接
  activeTodos: 活跃待办
  add: 添加
  addCategory: 添加分类
  addLink: 添加链接
  addMemo: 添加备忘录
  addNewTodo: 添加新待办
  addTask: 添加任务
  addTodo: 添加待办
  adding: 添加中
  admin: 管理面板
  all: 全部
  allDates: 所有日期
  allDomainSubmissionsProcessed: 所有域名提交都已处理
  allPlatformsLoaded: 所有平台已加载
  allSubmissionsReviewed: 所有提交都已审核
  allTags: 所有标签
  approve: 已批准
  approved: 已批准
  approvedDomains: 已批准域名
  approvedDomainsDescription: 显示所有已批准的域名列表
  bannerTextConfiguration: 横幅文字配置
  barChart: 柱状图
  browseTrendingTopics: 浏览热门话题
  cancel: 取消
  categories: 分类
  category:
    cannotDeleteWithSubcategories: 无法删除有子分类的分类
    createFailed: 创建分类失败
    createSuccess: 分类创建成功
    deleteFailed: 分类删除失败
    deleteSuccess: 分类删除成功
    nameRequired: 分类名称是必需的
    updateFailed: 更新分类失败
    updateSuccess: 分类更新成功
  categoryName: 分类名称
  categoryStatistics: 分类统计
  categoryUpdatedSuccessfully: 分类更新成功
  clear: 清空搜索
  clearPriority: 清除优先级
  clickDetails: 点击详情
  clicks: 👆 点击
  close: 关闭
  comparison:
    browseCategories: 浏览分类
    categoryFiltering: 分类筛选
    createShortUrls: 创建短链接
    customCategories: 自定义分类
    emailsUpTo30Days: 邮件保存30天
    feature: 功能
    guestUsers: 游客用户
    multiPlatforms: 多平台聚合
    navigationDirectory: 导航目录
    permanentUrls: 永久链接
    privateCollections: 私人收藏
    registeredUsers: 注册用户
    saveHotTopics: 保存热点
    submitDomains: 提交域名
    submitLinks: 提交链接
    tempEmails: 临时邮件
    temporaryEmail: 临时邮箱
    todaysHotList: 今日热榜
    trackClicks: 点击统计
    urlShortening: URL短链
    viewHotLists: 查看热榜
  confirm: 确认密码
  confirmDeleteCategoriesMessage_one: 您确定要删除此分类吗？此操作无法撤销。
  confirmDeleteCategoriesMessage_other: "您确定要删除这 {{count}} 个分类吗？此操作无法撤销。"
  confirmDeleteUrlMessage: 您确定要删除这个短链接吗？此操作无法撤销。
  confirmDeletion: 确认删除
  confirmRejectSubmission: 您确定要拒绝这个提交吗？
  confirmRejection: 确认拒绝
  contentDisplayError: 内容显示错误
  copy: 复制
  copyUrl: 复制链接
  country: 国家
  create: 创建分类
  createCategory: 创建分类
  createCategoryFirst: 请先创建一个分类
  createManagePersonalNavigation: 创建和管理您的个人导航分类和链接
  created: 创建时间
  createdAt: 创建时间
  createdEmails: 已创建邮件
  createdUrls: 已创建网址
  creating: 创建中...
  currentLanguage: 当前语言
  customizeFeaturesVisibilityOrder: 自定义功能可见性和顺序
  dailyTraffic: 每日流量
  dashboard: 控制台
  dashboardLayout: 控制台布局
  dataHasBeenUpdated: 数据已更新
  default: 一站式工具集
  defaultForAllUsers: 所有用户默认
  defaultLabel: 默认
  delete: 删除
  deleteAction: 删除
  deleteSelected: 删除选中
  deleteUrl: 删除链接
  detailedVisitorTrafficAnalysis: 详细访客流量分析
  disabled: 已禁用
  dismiss: 忽略
  domain: 域名
  emailAddress: 邮箱地址
  expiresAt: 过期时间
  showingEmailsCount: 显示 {{start}} - {{end}} 项，共 {{total}} 个邮箱
  showingDomains: 显示第{{start}}到{{end}}项，共{{total}}个域名
  domainActions:
    domainAlreadyExists: 域名 {{domain}} 已存在
    domainAddedToWhitelist: 域名 {{domain}} 已添加到白名单
    addedMainDomainInstead: 已添加主域名 {{domain}} 替代子域名
    domainApprovalStatusUpdated: 域名批准状态已更新
    domainApprovedSuccess: 域名批准成功
    domainDeletedSuccess: 域名删除成功
    domainRejectedAndRemoved: 域名已拒绝并移除
    failedToAddDomain: 添加域名失败
    failedToApproveDomain: 批准域名失败
    failedToDeleteDomain: 删除域名失败
    failedToRejectDomain: 拒绝域名失败
    failedToUpdateDomainStatus: 更新域名状态失败
  domainData:
    loginRequiredForAdmin: 您必须登录才能访问管理员功能
  domainDetails:
    alreadyWhitelisted: 域名已在白名单中
    cannotBeEmpty: 域名不能为空
    noDomainFound: 未找到域名
    submitFailed: 提交域名失败
    submittedForApproval: 域名已提交审核
    tryAdjustingSearch: 尝试调整搜索关键词
  domainTraffic: 域名流量
  domainVisits: 域名访问
  domainWhitelist:
    description: 管理允许的域名列表
    title: 域名白名单
  domainsAwaitingApproval: 等待审核的域名
  dragDropReordering:
    categoriesReorderFailed: 分类重新排序失败
    categoriesReorderedSuccess: 分类重新排序成功
    linksReorderFailed: 链接重新排序失败
    linksReorderedSuccess: 链接重新排序成功
  dueDate: 截止日期
  dueDateFilter: 截止日期筛选
  edit: 编辑
  editAction: 编辑
  editCategory: 编辑分类
  editInManager: 在管理器中编辑
  editLink: 编辑链接
  editTodo: 编辑待办
  email: 临时邮箱
  emailAlreadyGenerated: 邮箱已生成
  emailAlreadyGeneratedDesc: 您已经有一个活跃的临时邮箱地址
  emailDetails: 邮件详情
  emailPrefixRequired: 邮箱前缀是必需的
  emails: 临时邮箱
  enabled: 已启用
  enterCategoryName: 输入分类名称
  enterIconUrl: 输入图标URL
  enterLinkName: 输入链接名称
  error: 错误
  expiration: 过期时间
  external: 外部链接
  failedToApproveLink: 批准链接失败
  failedToCreateShortUrl: 创建短链接失败
  failedToCreateTemporaryEmail: 创建临时邮箱失败
  failedToDeleteCategories: 删除分类失败
  failedToDeleteLinks: 删除链接失败
  failedToFetchUrls: 获取网址失败
  failedToLoadEmails: 加载邮件失败
  failedToLoadFeatures: 加载功能失败
  failedToLoadLinks: 加载导航链接失败
  failedToRejectLink: 拒绝链接失败
  failedToSaveFeatureOrder: 保存功能顺序失败
  failedToUpdateCategory: 更新分类失败
  failedToUpdateLink: 链接更新失败
  featureOrderSaved: 功能顺序已保存
  featureOrdering:
    dashboardPreferencesSaved: 控制台偏好已保存
    failedToSaveDashboard: 保存控制台配置失败
    failedToSaveHomepage: 保存主页配置失败
    homepageConfigSaved: 主页配置已保存
    loginRequired: 您需要登录才能访问此页面。
  featuresGrid:
    autoExpiration: 自动过期清理
    categorizedWebsites: 分类整理的优质网站
    checkNow: 立即查看
    clickAnalytics: 点击分析
    customShortCodes: 自定义短链代码
    emailFeature: 临时邮箱
    emailFeatureDesc: 生成临时邮箱地址保护隐私
    exploreNow: 立即探索
    multiplePlatforms: 多平台内容聚合
    navigationDirectory: 导航目录
    navigationDirectoryDesc: 浏览和发现按类别组织的实用工具和资源
    noRegistrationRequired: 无需注册
    publicPrivateCollections: 公开和私人收藏
    realTimeUpdates: 实时更新
    todaysHotList: 今日热榜
    todaysHotListDesc: 了解来自多个平台的热门内容
    tryNow: 立即试用
    urlFeature: 短链接工具
    urlFeatureDesc: 创建短小易分享的链接并进行分析
  featuresOrder: 功能排序
  getStarted: 免费开始使用
  high: 高
  highPriority: 高优先级
  home:
    designResources: 设计资源
    developmentTools: 开发工具
    exploreNavigation: 探索导航目录
    featuredCategories: 精选分类
    frequentlyUsed: 常用
    learning: 学习
    links: 链接
    navigation: 导航目录
    navigationDescription: 浏览和发现实用工具和资源
    navigationDirectory: 导航目录
    poweredBy: 技术支持
    premium: 高级版
    productivity: 效率工具
    recentlyVisited: 最近访问
    searchResources: 搜索资源...
  homepageLayout: 主页布局
  hotNews:
    categorizedContent: 分类内容
    categorizedContentDesc: 按类别浏览不同平台的热门内容
    exploreTrendingTopics: 探索热门话题
    loginToViewHotTopics: 登录查看热点话题
    noPlatformsAvailable: 暂无平台可用
    realTimeHotTopics: 实时热点话题
    realTimeUpdates: 实时更新
    realTimeUpdatesDesc: 获取最新的热门话题和趋势
    serviceUnavailable: 服务暂时不可用
    signIn: 登录
    signInToAccess: 登录以访问最新的热门内容和趋势
    smartFiltering: 智能筛选
    smartFilteringDesc: 根据您的兴趣筛选和个性化推荐
    stayUpdated: 及时了解最新趋势和热点
    todaysTrendingTopics: 今日热门话题
    viewTopics: 查看话题
    viewMoreTopics: 查看更多话题
  hotNewsRanking: 热新闻排行
  hotValue: 热度值：
  icon: 图标
  iconLabel: 图标
  iconUrlOptional: 图标URL（可选）
  iconUrlOrLeaveEmpty: 图标URL或留空
  inactive: 非活跃
  internal: 内部链接
  internalLink: 内部链接
  items: 项
  jumpToLatest: 跳转到最新
  lastUpdated: 最后更新
  leaveEmptyForTopLevel: 留空以创建顶级分类
  link:
    createFailed: 创建链接失败
    createSuccess: 链接创建成功
    deleteFailed: 链接删除失败
    deleteSuccess: 链接删除成功
    reorderFailed: 链接重新排序失败
    reorderSuccess: 链接重新排序成功
    updateFailed: 链接更新失败
    updateSuccess: 链接更新成功
  linkApproved: 链接已批准
  linkApprovedSuccessfully: 链接已成功批准
  linkName: 链接名称
  linkRejected: 链接已拒绝
  linkRejectedSuccessfully: 链接已成功拒绝
  linkSubmissionModeration: 链接提交审核
  linkUpdatedSuccessfully: 链接更新成功
  links: 链接
  linksCount: 链接数量
  loadPlatformListFailed: 加载平台列表失败
  loaded: 二维码已下载
  loadedCount: 已加载
  loading: 加载中
  loadingApprovedSubmissions: 正在加载已批准的提交...
  loadingBannerConfiguration: 正在加载横幅配置...
  loadingMorePlatforms: 正在加载更多平台...
  loadingPlatforms: 正在加载平台数据
  loadingPlatform: 正在加载平台数据...
  loadingRejectedSubmissions: 正在加载已拒绝的提交...
  loadingResources: 正在加载资源...
  loadingSubmissions: 正在加载提交...
  locale: 语言
  login: 登录
  loginPrompt:
    description: 管理允许的域名列表
    title: 域名白名单
  logout: 退出登录
  logoutFailed: 登出失败
  logoutSuccess: 登出成功
  low: 允许的域名
  lowPriority: 低优先级
  mainCategories: 主分类
  mainCategory: 主分类
  mainLabel: 主分类
  manage: 管理
  manageNavigationLinksDesc: 管理您的导航链接和分类
  medium: 中等
  messagesCount: 消息
  mediumPriority: 中等优先级
  memoDetails: 备忘录详情
  memoList: 备忘录列表
  memos: 备忘录
  messages:
    error:
      emptyInput: 输入不能为空
      generationError: 生成过程中出错
    errors:
      base64Conversion: Base64转换错误
      emptyInput: 输入不能为空
      generationError: 生成时出错
      invalidBase64: 无效的Base64格式
    hash:
      algorithm: 算法
      description: 生成文本的哈希值
      enterTextToHash: 输入要哈希的文本
      generateHash: 生成哈希
      hashResult: 哈希结果
      inputText: 输入文本
      title: 哈希生成器
    history:
      decode: 解码
      encode: 编码
    success:
      copiedToClipboard: 已复制到剪贴板
    tools:
      base64Converter: Base64转换器
      base64EncodingWillAppearHere: Base64编码将显示在此处
      convertBetweenPlainTextAndBase64: 在纯文本和Base64之间转换
      copy: 复制
      decode: 解码
      decodedTextWillAppearHere: 解码文本将显示在此处
      encode: 编码
      enterBase64ToDecode: 输入要解码的Base64
      enterTextToEncode: 输入要编码的文本
      inputBase64: 输入Base64
      inputText: 输入文本
      outputBase64: 输出Base64
      outputText: 输出文本
      urlSafeMode: URL安全模式
  mode: 模式
  mustBeLoggedIn: 您必须登录
  myMemos: 我的备忘录
  myPersonalNavigation: 我的个人导航
  mySubmissions: 我的提交
  myTasks: 我的待办
  name: 名称
  nameLabel: 名称
  navigation:
    about: 关于我们
    aboutUs: 关于我们
    all: 全部
    allCategories: 所有分类
    allResources: 所有资源
    categories:
      aiTools: AI工具
      deployment: 部署平台
      designTools: 设计工具
      devTools: 开发工具
      images: 图片素材
      productivity: 效率工具
    categoriesLabel: 分类
    categoryManagement: 分类管理
    categoryManagementDesc: 智能分类，轻松管理
    clearSearch: 清除搜索
    connectionFailed: 连接失败
    createPersonal: 创建个人导航
    darkMode: 深色模式
    exploreNow: 立即探索
    external: "{{name}} - 外部工具"
    featured: 精选功能
    featuresIntro: 功能介绍
    filterByCategory: 按分类筛选
    internal: "{{name}} - 内部工具"
    lightMode: 浅色模式
    loadingData: 正在加载数据...
    logoAlt: g2.al 标志
    memoAndTodo: 备忘录
    myDirectory: 我的导航目录
    noCategoriesAvailable: 没有可用的分类
    noLinksInCategory: 此分类中没有可用的链接。添加您的第一个链接！
    noResourcesInCategory: 此分类中未找到资源
    noResultsFound: "未找到 \\\"{{query}}\\\" 的结果"
    online: 在线工具
    open: 打开
    openInNewTab: 在新标签页中打开
    personal: 个人导航
    quickAdd: 快速添加
    quickBookmarks: 快速收藏
    quickBookmarksDesc: 一键收藏，随时访问
    quickSearch: 快速搜索...
    recentlyAdded: 最近添加
    recommended: 推荐
    searchPlaceholder: 搜索网站和工具...
    showingResourcesInCategory: "正在显示{{category}}中的资源"
    smartDirectory: 智能导航目录
    smartDirectoryDescription: 打造您的专属网站导航，智能分类管理，高效访问您的常用网站和工具
    smartSearch: 智能搜索
    smartSearchDesc: 快速定位，高效查找
    socialSharing: 社交分享
    socialSharingDesc: 分享推荐，互助成长
    toggleLanguage: 切换语言
    tryDifferentCategory: 尝试浏览不同的分类
    unavailable: 服务不可用
    viewAllCategories: 查看所有分类
  navigationCategoryAnalytics: 导航分类分析
  navigationManagement: 导航管理
  never: 永不过期
  new: 新账户创建成功
  newsCount: 新闻数量：
  no: 否
  noActiveTodos: 没有进行中的待办
  noApprovedDomainsFound: 未找到已批准的域名
  noApprovedSubmissions: 没有已批准的提交
  noCategoriesFoundCreateFirst: 未找到分类，请先创建一个
  noCategoriesFoundData: 未找到分类
  noContent: 无内容
  noCountryDataAvailable: 无国家数据可用
  noDataFallback:
    locationDataDescription: 位置数据可用时将显示在此处
    noLocationDataAvailable: 无位置数据可用
    reloadMap: 重新加载地图
  noDate: 无日期
  noDomainTrafficDataAvailable: 无域名流量数据可用
  noDomainsFound: 未找到域名
  noDomainsInWhitelist: 白名单中没有域名
  noEmailsReceivedYet: 尚未收到任何邮件
  noHotListDataAvailable: 无热榜数据可用
  noLabel: 否
  noLinksFoundAddFirst: 此分类中未找到链接。添加您的第一个链接！
  noMatchingMemos: 没有匹配的备忘录
  noMatchingTasks: 没有匹配的任务
  noMemosYet: 暂无备忘录
  noNewsAvailable: 暂无新闻
  noPageVisitDataAvailable: 无页面访问数据可用
  noParentTopLevel: -- 无父分类（顶级）--
  noPendingDomains: 没有待审核的域名
  noPendingSubmissions: 没有待审核的提交
  noRejectedSubmissions: 没有已拒绝的提交
  noShortUrlVisitsDataAvailable: 无短链接访问数据可用
  noShortUrlsCreatedYet: 尚未创建任何短链接
  noSubject: 无主题
  noTasksYet: 暂无任务
  noTemporaryEmailsCreated: 还没有创建临时邮箱
  noTimelineDataAvailable: 无时间线数据可用
  noTodosYet: 暂无待办
  noUsersFound: 未找到用户
  notApproved: 未批准
  openUrl: 打开链接
  orderUpdatedRememberSave: 顺序已更新。记得保存您的更改。
  originalUrl: 原始网址
  overdue: 已逾期
  pagePopularity: 页面热度
  pageViewsPV: 页面浏览量（PV）
  parentCategory: 父分类（可选）
  parentCategoryOptional: 父分类（可选）
  pending: 等待审核
  pickADate: 选择日期
  pieChart: 饼图
  platformDistribution: 平台分布和热门新闻排行
  pleaseEnterUrl: 请输入网址
  pleaseSelectCategory: 请选择分类
  pleaseWait: 请稍候
  powerfulFeatures: 强大功能
  preview: 预览
  priority: 优先级
  profile: 个人资料
  progress: 进度
  public: 公开
  qrCode: 二维码已下载
  qrCodeForYourUrl: 您的链接二维码
  recentItems: 最近项目
  recentMemos: 最近备忘录
  recentMessages: 最近消息
  refreshAll: 刷新全部
  refreshComplete: 刷新完成
  refreshDataFailed: 刷新数据失败
  refreshingData: 正在刷新数据...
  register: 注册
  registeredUsers: 注册用户
  reject: 已拒绝
  rejected: 已拒绝
  reload: 重新加载
  reset: 重置为默认设置
  resetToDefaultRememberSave: 已重置为默认。记得保存您的更改。
  reviewAndApproveLinks: 审核并批准用户提交的链接
  role: 角色
  saveChanges: 保存更改
  saveLayout: 保存布局
  saveOrder: 保存顺序
  saving: 保存中
  search: 搜索待办事项...
  searchDomains: 搜索域名...
  searchMemosPlaceholder: 搜索备忘录...
  searchTasksPlaceholder: 搜索待办事项...
  searchTodosAndMemos: 搜索待办和备忘录...
  searchUsers: 搜索用户
  selectAll: 全选
  selectCategory: 选择分类
  selectCategoryItem: 选择分类项
  selectCategoryToViewLinks: 选择一个分类以查看其链接
  shortUrl: 短链接
  shortUrlVisits: 短链接访问
  shortUrls: 短链接
  showingUsersCount: "显示 {{count}} 个用户"
  showingUsersCount_one: "显示 {{count}} 个用户"
  showingUsersCount_other: "显示 {{count}} 个用户"
  siteVisitorStatistics: 网站访客统计
  statistics: 统计信息
  status: 状态
  subLabel: 子分类
  subcategories: 子分类
  submit: 提交
  submitDomain: 提交域名
  submitDomainForWhitelist: 提交域名到白名单
  submitDomainWhitelistDescription: 提交域名到白名单以供审核
  submitted: 提交的链接将显示在这里
  submittedBy: "提交者:"
  submitting: 提交中...
  success: 已复制到剪贴板
  superAdmin: 超级管理员
  systemSettings: 系统设置
  tags: 标签
  tasks: 待办事项
  tempEmail:
    createFailed: 创建临时邮箱失败
    createSuccess: S3配置创建成功
    noEmailsYet: 暂无邮件
  tempEmails: 临时邮件
  tempMailbox:
    autoDeleteNotice: 邮件将在24小时后自动删除
    cancel: 取消
    copiedToClipboard: 已复制到剪贴板
    copyEmailFailed: 复制邮箱地址失败
    copyFailed: 复制失败
    copyToClipboard: 复制到剪贴板
    debugMessages:
      copyToClipboardFailed: 复制到剪贴板失败
      fetchEmailsFailed: 获取邮件失败
      generateTempEmailFailed: 生成临时邮箱失败
    delete: 删除
    deleteConfirmation: 确定要删除这个临时邮箱吗？这将删除所有相关邮件。
    deleteEmail: 删除邮箱
    deleteTempEmail: 删除临时邮箱
    description: 生成临时邮箱地址来接收邮件
    emailAddressCopied: 邮箱地址已复制
    emailDeleted: 邮件已删除
    emailDeletedDesc: 临时邮箱已成功删除
    emailGenerated: 邮箱已生成
    emailGeneratedDesc: 临时邮箱地址已成功生成
    fetchEmailsFailed: 获取邮件失败
    from: 来自：
    generateEmail: 生成邮箱
    generateFailed: 生成邮箱失败
    inbox: 收件箱
    noMessages: 暂无邮件
    noTempEmail: 没有临时邮箱
    noTempEmailDesc: 点击下方按钮生成一个临时邮箱地址
    selectEmail: 选择邮件查看详情
    tempEmailInbox: 临时邮箱收件箱
    title: 临时邮箱
    validFor24Hours: 邮箱有效期24小时
    viewAll: 查看全部
    viewInbox: 查看收件箱
    yourTempEmail: 您的临时邮箱
  temporaryEmailCreated: 临时邮箱创建成功
  temporaryEmails: 临时邮箱
  today: 今日
  todayDate: 今日
  todaysHotList: 今日热榜
  todaysHotListAnalytics: 今日热榜分析
  todoTitle: 待办标题
  todosAndMemos: 待办与备忘录
  tomorrow: 明天
  totalClicks: 总点击数
  totalUsers: 总用户数
  totalVisits: 总访问量
  trackManageShortUrls: 跟踪和管理您的短链接
  trackSubmittedNavigationLinks: 跟踪您提交的导航链接
  trafficByDomain: 按域名统计流量
  tryAdjustingSearchOrAddDomain: 尝试调整搜索条件或添加新域名
  tryAgain: 重试
  type: 类型
  typeLabel: 类型
  unexpectedError: 发生了意外错误
  uniqueVisitors: 独立访客
  uniqueVisitorsUV: 独立访客（UV）
  unknown: 未知
  unverified: 未验证
  upcoming: 即将到来
  update: S3配置更新成功
  url: 网址
  urlCopiedToClipboard: 网址已复制到剪贴板
  urlDetails: 网址详情
  urlLabel: 网址
  urlShortenedSuccessfully: 网址缩短成功
  urlShortener:
    dailyLimitReached: "已达到每日限制（{{limit}} 个网址）。请明天再试。"
    domainNotInWhitelist: 域名不在白名单中
    loginRequiredForPermanent: 永久链接需要登录
    pleaseEnterUrl: 请输入网址
    urlShortenFailed: 网址缩短失败
    urlShortenedSuccess: 网址缩短成功
  urlStats: 短链接统计
  urlVisits: 网址访问
  urls: 网址
  userData:
    failedToDeleteUser: 用户删除失败
    failedToLoadUsers: 加载用户失败
    failedToUpdateUser: 用户更新失败
    userDeletedSuccess: 用户删除成功
    userUpdatedSuccess: 用户更新成功
  userDetails: 用户详情
  userManagement: 用户管理
  userManagementDescription: 管理系统用户
  username: 用户名
  users: 用户管理
  verified: 已验证
  view: 视图
  viewAllReceivedEmails: 查看所有收到的邮件
  viewAllTodosAndMemos: 查看所有待办和备忘录
  visibilityUpdatedRememberSave: 可见性已更新。记得保存您的更改。
  visible: 显示
  visitorMap: 访客地图
  visits: 访问量
  yes: 是
  yesLabel: 是
  yourTemporaryEmailAddresses: 您的临时邮箱地址
  yourUrls: 您的网址
createAccount: 创建账户
cta:
  allFeatures: 全功能
  alwaysFree: 永久免费
  completelyFree: 完全免费
  exploreFeatures: 探索功能
  getStartedDescription: 立即体验我们的一站式工具平台，无需信用卡，完全免费
  getStartedFree: 免费开始使用
  instantAccess: 即时访问
  limitedOffer: 限时优惠
  noCredit: 无需信用卡
  openSource: 开源项目
  startFreeNow: 立即免费开始
dashboard:
  copiedToClipboard: 已复制到剪贴板
  description: 管理您的短链接和临时邮箱
  emailDeletedSuccessfully: 邮件删除成功
  failedToCreateTempEmail: 创建临时邮箱失败
  failedToDeleteEmail: 删除邮件失败
  failedToDeleteUrl: 删除链接失败
  failedToLoadData: 加载数据失败
  features:
    dashboardLayout: 控制台布局
    myNavigation: 我的导航
    temporaryEmail: 临时邮箱
    urlShortener: 短链接工具
  noUserIdAvailable: 无可用用户ID
  pleaseLoginFirst: 请先登录
  tempEmailCreateNotImplemented: 临时邮箱创建未实现
  tempEmailCreated: 临时邮箱已创建
  tempEmailDeleteNotImplemented: 临时邮箱删除未实现
  tempEmailFeatureNotAvailable: 临时邮箱功能不可用
  tempEmailsApiNotAvailable: 临时邮箱API不可用
  tempMailboxApiNotImplemented: 临时邮箱API未实现
  title: 控制台
  urlDeletedSuccessfully: 链接删除成功
dragDropInstructions: 拖拽重新排序链接
emailExpiration: 邮箱过期
emailExpirationDesc: 临时邮箱为了安全自动过期
emailExpirationOption1: 24小时 - 快速验证
emailExpirationOption2: 7天 - 短期使用
emailExpirationOption3: 30天 - 扩展项目
emailFeature1Desc: 即时生成临时邮箱地址
emailFeature1Title: 即时邮箱创建
emailFeature2Desc: 保护您的真实邮箱地址隐私
emailFeature2Title: 隐私保护
emailFeature3Desc: 避免在主邮箱中收到不需要的邮件
emailFeature3Title: 垃圾邮件防护
emailFeature4Desc: 无需创建账户即可使用
emailFeature4Title: 无需注册
emailFeature5Desc: 创建多个临时邮箱
emailFeature5Title: 多邮箱支持
emailFeature6Desc: 邮件在过期后自动删除
emailFeature6Title: 自动清理
emails:
  createEmail: 创建邮箱
  createEmailDescription: 生成临时邮箱地址满足您的需求。
emptyState:
  categoryWaitingForResources: 此分类正在等待添加精彩资源
  clearSearch: 清除搜索
  discoverAmazingWebsites: 发现精彩网站和工具，或贡献您的收藏
  manageNavigation: 管理导航
  refresh: 刷新
  tryAdjustingSearch: 尝试调整搜索条件或探索不同的分类
  welcomeToNavigationHub: 欢迎来到导航中心！
enterLongUrl: 输入您的长网址
error:
  accessForbidden: 访问被禁止
  authenticationRequired: 需要身份验证
  createAccount: 创建账户
  goToHome: 返回首页
  login: 登录
  loginRequired: 需要登录
errors:
  pageNotFound: 页面未找到
  pageNotFoundDescription: 您要找的页面不存在或已被移动。
features:
  arrangeItems: 排列项目
  arrangeItemsDescription: 拖拽重新排序功能项目并设置其可见性
  component:
    name: 名称
  dragDropInstructions: 拖拽项目以重新排序。别忘了保存您的更改。
  resetToDefault: 重置为默认设置
  visible: 显示
form:
  allFieldsRequired: 所有字段都是必需的
generateTempEmail: 生成临时邮箱
getShortUrl: 获取您的短链接
home:
  features:
    navigationDirectory:
      description: 浏览精选网站集合和工具
      linkText: 探索导航
      title: 导航目录
    temporaryEmail:
      linkText: 获取临时邮箱
    todaysHotList:
      description: 来自各大平台的热门内容
      linkText: 查看热点话题
      newLabel: 最新
      title: 今日热榜
    urlShortening:
      linkText: 试用短链接工具
  floatingWidget: 浮动窗口
  floatingWidgetDescription: 随时随地访问您的待办和备忘录
  openTodoMemo: 打开待办与备忘录
  quickNotes: 快速笔记
  quickNotesDescription: 即时捕捉并整理您的想法
  taskManagement: 任务管理
  taskManagementDescription: 组织和跟踪您的日常任务
  todoMemoDescription: 高效组织任务并捕捉想法
  todoMemoManager: 待办与备忘录管理器
hotNews:
  analytics: 数据分析
  analyticsDesc: 深入了解热点数据
  entertainment: 娱乐
  hotNews: 今日热榜
  others: 其他
  technology: 科技
  customizeFeeds: 定制专属信息流
  dataSource: 数据来源
  description: 汇聚全网热点资讯
  errorLoadingNews: 加载新闻失败
  exploreNow: 立即探索
  externalAPI: 外部API
  failedToLoad: 加载失败
  failedToLoadPlatforms: 加载平台列表失败
  fetchFailed: "获取 {platform} 数据失败"
  fetchSuccess: "成功获取 {platform} 数据，共 {count} 条"
  fetchingData: "正在获取 {platform} 数据..."
  hotness: 热度
  loadingDescription: 正在加载热榜数据，请稍候...
  lastUpdate: 最后更新
  localData: 本地数据
  multiPlatform: 多平台聚合
  multiPlatformDesc: 汇聚各大平台热点
  noData: 暂无数据
  noDataAvailable: "{platform} 暂无数据"
  noHotTopicsAvailable: 暂无热榜数据
  noNewsAvailable: 暂无新闻
  platformCategories:
    entertainment: 娱乐资讯
    news: 新闻资讯
    others: 其他
    socialMedia: 社交媒体
    technology: 科技资讯
  platforms:
    list: 平台列表
    "/36kr": 36氪
    "/baidu": 百度热搜
    "/bilibili": 哔哩哔哩
    "/douban-movie": 豆瓣电影
    "/douyin": 抖音热点
    "/hupu": 虎扑
    "/huxiu": 虎嗅
    "/ithome": IT之家
    "/juejin": 掘金
    "/netease-news": 网易新闻
    "/weibo": 微博热搜
    "/zhihu": 知乎热榜
  realTimeUpdates: 实时更新
  realTimeUpdatesDesc: 第一时间获取最新动态
  refresh: 刷新
  refreshPlatform: 刷新
  refreshing: "正在刷新 {platform}..."
  retry: 重试
  retrying: 正在重试...
  serviceNotice: 热榜数据来自第三方API，可能偶尔不稳定
  title: 今日热榜
  todaysHotList: 今日热榜
  todaysTrending: 今日热点
  trending: 热门
  trendingDesc: 追踪热门话题趋势
  trendingToday: 今日热点趋势
  trendingTodayDescription: 实时汇聚全网热点，掌握最新趋势动态，发现有价值的内容
  view: 查看
index:
  addLinks: 添加链接
  exploreNavigationDirectory: 探索导航目录
  heroDescription: 探索强大的工具集合，旨在简化您的数字生活，包括URL缩短器、临时邮箱、导航管理和实时热门内容。
  heroSubtitle: 一体化平台
  heroTitle: 您的数字中心
  hot: 热门
  hotListsDescription: 了解最新趋势话题
  myNavigation: 我的导航
  navigationDirectoryDescription: 发现有用的网站和工具，或添加您自己的收藏夹以便快速访问
  new: 新功能
  noHotTopicsAvailable: 暂无热榜数据
  noNavigationLinks: 您还没有创建任何导航链接
  todaysHotLists: 今日热榜
  todaysHotTopics: 今日热榜
  viewAllHotTopics: 查看全部热榜
  viewAllNavigation: 查看全部导航
  viewHotLists: 查看热榜
  viewNavigationDirectory: 查看导航目录
joinUs: 加入我们
joinUsDesc: 成为我们不断增长的社区的一部分
layout:
  allRightsReserved: 版权所有
  contact: 联系我们
  privacy: 隐私
  terms: 条款
learnMoreFeatures: 了解更多功能
linkSubmission:
  category: 分类
  enterLinkName: 输入链接名称
  failedToCreateLink: 创建链接失败
  fillAllRequiredFields: 请填写所有必填字段
  iconUrlOptional: 图标URL（可选）
  iconUrlOrLeaveEmpty: 图标URL或留空
  internalLink: 内部链接
  internalLinkDescription: （如果此链接指向本站内页面请勾选）
  linkName: 链接名称
  linkSubmittedForReview: 链接已提交审核
  mustBeLoggedInToSubmit: 您必须登录才能提交链接
  noCategoriesAvailable: 没有可用的分类
  noCategoriesDescription: 目前没有可用于提交的公共分类。
  selectAppropriateCategory: 为您的链接选择最合适的分类
  submitLinkForReview: 提交链接以供审核
  submitNewLink: 提交新链接
  submitting: 提交中...
  url: 网址
  urlPlaceholder: "https://example.com"
maxDaysLimit: 出于安全考虑最长30天
memo:
  displayError: 内容显示错误
  noContent: 无内容
navigation:
  about: 关于我们
  aboutUs: 关于我们
  addAnotherLink: 添加另一个链接
  addLink: 添加链接
  addLinks: 添加链接
  addNewLink: 添加新链接
  all: 全部
  allLinks: 所有链接
  approved: 已批准
  autoDetectingFavicon: 正在自动检测图标...
  categories:
    aiTools: AI工具
    deployment: 部署平台
    designTools: 设计工具
    devTools: 开发工具
    images: 图片素材
    productivity: 效率工具
  category: 分类名称
  categoryKeywords:
    design: 设计
    dev: 开发
    develop: 开发
    edu: 学习
    entertain: 娱乐
    learn: 学习
    news: 新闻
    shop: 购物
    social: 社交
    tool: 工具
  categoryManagement: 分类管理
  categoryManagementDesc: 智能分类，轻松管理
  connectionFailed: 连接失败
  createLink: 创建链接
  createPersonal: 创建个人导航
  darkMode: 深色模式
  deleteLink: 删除链接
  editLink: 编辑链接
  enterLinkName: 输入链接名称
  exploreNow: 立即探索
  exploreResource: 点击探索这个资源
  failedToCreateLink: 创建链接失败
  failedToDeleteLink: 删除链接失败
  failedToLoadLinks: 加载导航链接失败
  failedToSubmitLink: 提交链接到公共目录失败
  failedToSubmitLinkToPublic: 提交链接到公共区域失败
  failedToSubmitLinks: 提交链接失败
  failedToUpdateLink: 链接更新失败
  faviconWillBeAutoDetected: 图标将自动检测
  featured: 精选推荐
  featuresIntro: 功能介绍
  filterByCategory: 按分类筛选
  fullNavigationPage: 完整导航页面
  home: 首页
  iconPlaceholder: "https://示例.com/favicon.ico"
  iconUploader:
    add: 添加
    addIcon: 添加图标
    addIconButton: 添加图标
    cancel: 取消
    enterIconUrl: 输入图标URL
    failedToLoadImage: 无法加载图像。请检查URL。
    orPasteUrl: 或粘贴公开可访问的图像URL。建议尺寸：64x64像素。
    pleaseEnterIconUrl: 请输入图标URL
    preview: 预览
    uploadOrAddIcon: 上传或添加图标
  iconUrl: 图标URL或留空
  iconUrlOrLeaveEmpty: 图标URL或留空
  internalLink: 内部链接
  layout:
    all: 全部
    discoverWebsites: 发现优秀网站和工具
    navigationHub: 导航中心
    searchPlaceholder: 搜索网站和工具...
    sortOptions:
      az: 📝 名称
      clicks: 👆 点击
      date: 📅 时间
      hot: 🔥 热门
      new: 🕒 最新
  lightMode: 浅色模式
  linkCard:
    visit: 访问
  linkDeletedSuccessfully: 链接删除成功
  linkName: 链接名称
  linkSubmittedForReview: 链接已提交审核
  linkSubmittedSuccessfully: 链接已成功提交到公共目录
  linkSubmittedToPublic: 链接已提交到公共区域
  linkUpdatedSuccessfully: 链接更新成功
  linkUrl: 链接地址
  links: 链接
  linksList: 链接列表
  loadingSubcategories: 正在加载子分类...
  logoAlt: g2.al 标志
  main: 域名
  mainCategory: 主分类
  memoAndTodo: 备忘录
  moveDown: 向下移动
  moveUp: 向上移动
  myDirectory: 我的导航目录
  navigation: 导航
  navigationCenter: 导航中心
  navigationHub: 导航中心
  navigationHubDescription: 探索导航中心，发现有用的网站和工具
  noCategoriesAvailable: 没有可用的分类
  noCategoriesFoundCreateFirst: 未找到分类，请先创建一个
  noLinksInCategory: 此分类中没有可用的链接。添加您的第一个链接！
  noSubmittedLinksYet: 还没有提交的链接
  onlineTools: 在线工具
  pending: 等待审核
  personalNavigation: 个人导航
  pleaseAddAtLeastOneLink: 请至少添加一个链接
  pleaseSelectCategoryFirst: 请先选择一个分类来管理链接。
  quickAdd: 快速添加
  quickBookmarks: 快速收藏
  quickBookmarksDesc: 一键收藏，随时访问
  quickSearch: 快速搜索...
  recentlyAdded: 最近添加
  recommended: 推荐导航
  rejected: 已拒绝
  selectMainCategory: 选择主分类
  selectPublicCategory: 选择公共分类
  selectSubcategory: 选择子分类
  smartDirectory: 智能导航目录
  smartDirectoryDescription: 打造您的专属网站导航，智能分类管理，高效访问您的常用网站和工具
  smartSearch: 智能搜索
  smartSearchDesc: 快速定位，高效查找
  socialSharing: 社交分享
  socialSharingDesc: 分享推荐，互助成长
  sortOrderUpdateFailed: 更新排序顺序失败
  sortOrderUpdated: 排序顺序已更新
  sub: 提交
  subcategory: 子分类
  submitLink: 提交链接
  submitLinkToPublicDescription: 将此链接提交到公共目录供其他用户查看
  submitLinks: 提交链接
  submitToPublicDirectory: 提交到公共目录
  submittedLinksWillAppearHere: 提交的链接将显示在这里
  timeUnit: 毫秒
  title: 导航中心
  description: 现代化的导航中心，快速访问常用工具和服务
  todaysHotList: 今日热榜
  toggleLanguage: 切换语言
  toggleMenu: 切换菜单
  urlPlaceholder: "https://示例.com"
  searchCards: 搜索导航卡片...
  loadFailed: 加载失败
  loadFailedDesc: 无法加载导航数据
  orderUpdated: 排序已更新
  orderUpdatedDesc: 卡片排序已成功更新
  noCardsFiltered: 没有符合条件的卡片
  noCardsAvailable: 暂时没有可用的导航卡片
  addFirstCard: 添加第一个卡片
  cardUpdatedDesc: 导航卡片更新成功
  cardAdded: 卡片已添加
  cardAddedDesc: 新的导航卡片添加成功
  activeOnly: 仅显示正常
  downOnly: 仅显示失效
  internalOnly: 仅内部链接
  externalOnly: 仅外部链接
  checkConnectivityTooltip: 检查所有外部链接的连通性
  type: 类型
  filters: 过滤器
  connectivityCheckComplete: 连通性检查完成
  connectivityCheckCompleteDesc: 已检查 {{total}} 个外部链接
  allCategories: 所有分类
  allStatus: 所有状态
  allTypes: 所有类型
  internal: 内部链接
  external: 外部链接
  personal: 个人
  public: 公共
  
  # 新导航系统v2翻译
  searchPlaceholder: 搜索导航卡片...
  filterBy: 筛选
  viewType: 视图类型
  gridView: 网格视图
  listView: 列表视图
  compactView: 紧凑视图
  cardSize: 卡片大小
  small: 小
  medium: 中
  large: 大
  columns: 列数
  showDescriptions: 显示描述
  showRatings: 显示评分
  showStatus: 显示状态
  
  # 卡片操作
  addCard: 添加卡片
  editCard: 编辑卡片
  deleteCard: 删除卡片
  cardTitle: 卡片标题
  cardDescription: 卡片描述
  cardUrl: 卡片链接
  cardIcon: 卡片图标
  cardTags: 标签
  isInternal: 内部链接
  isPublic: 公开卡片
  cardWidth: 卡片宽度
  cardHeight: 卡片高度
  cardPosition: 卡片位置
  sortOrder: 排序权重
  
  # 分类操作
  addCategory: 添加分类
  editCategory: 编辑分类
  deleteCategory: 删除分类
  categoryColor: 分类颜色
  parentCategory: 父分类
  publicCategory: 公开分类
  
  # 评分
  rateCard: 为卡片评分
  rating: 评分
  averageRating: 平均评分
  userRatings: 用户评分
  ratingSubmitted: 评分提交成功
  
  # 连通性检查
  checkConnectivity: 检查连通性
  checking: 检查中
  lastChecked: 上次检查
  responseTime: 响应时间
  exitEdit: 退出编辑
  checkingConnectivity: 检查连通性中... {{current}}/{{total}}
  selectedCards: 已选择 {{count}} 张卡片
  addNewCard: 新增卡片
  iconView: 图标视图
  canvasView: 画布视图
  addApp: 添加应用
  addCardDesc: 创建或编辑导航卡片，支持分类管理和标签标记
  url: 网址
  titlePlaceholder: 输入网站标题
  descriptionPlaceholder: 简短描述这个网站或工具的用途
  deleteSelected: 删除选中
  allCards: 所有卡片
  myCards: 我的卡片
  publicCards: 公共卡片
  owner: 拥有者
  
  # 拖拽和布局
  editMode: 编辑模式
  exitEditMode: 退出编辑
  dragToReorder: 拖拽重新排序
  resizeCard: 调整卡片大小
  moveCard: 移动卡片
  layoutSaved: 布局已保存
  
  # 状态和错误
  success: 成功
  error: 错误
  loading: 加载中
  noCards: 暂无卡片
  noCategories: 暂无分类
  cardCreated: 卡片创建成功
  cardUpdated: 卡片更新成功
  cardDeleted: 卡片删除成功
  categoryCreated: 分类创建成功
  categoryUpdated: 分类更新成功
  categoryDeleted: 分类删除成功
  
  # 过滤器选项
  allOwners: 所有来源
  activeCards: 正常卡片
  downCards: 异常卡片
  internalLinks: 内部链接
  externalLinks: 外部链接
  
  # API集成
  apiIntegration: API集成
  integrationType: 集成类型
  endpoint: 端点地址
  authToken: 认证令牌
  refreshInterval: 刷新间隔
  lastSynced: 上次同步
  syncNow: 立即同步
navigationPreview:
  cancel: 取消
  doneReordering: 完成重新排序
  edit: 编辑
  editLink: 编辑链接
  error: 错误
  external: 外部
  failed: 失败
  failedToUpdateSortOrder: 更新排序顺序失败
  icon: 图标
  internal: 内部
  linkUpdateFailed: 链接更新失败
  linkUpdatedSuccess: 链接更新成功
  loadingPreview: 正在加载预览...
  name: 名称
  navigationPreview: 导航预览
  noCategoriesFound: 未找到分类
  noCategoryLinks: 此分类中没有链接
  ping: 延迟
  recommend: 推荐
  recommendLink: 推荐链接
  recommendationFailed: 推荐提交失败
  recommendationReview: 您的推荐将由管理员审核后添加到公共目录。
  recommendationSubmitted: 推荐提交成功
  reorderLinks: 重新排序链接
  saveChanges: 保存更改
  selectCategory: 选择分类
  sortOrderUpdated: 排序顺序已更新
  submitRecommendation: 提交推荐
  submitting: 提交中...
  timeUnit: 毫秒
  url: 网址
onlineTools:
  categories:
    all: 全部
    design: 设计工具
    development: 开发工具
    popular: 热门
    recent: 最近使用
    security: 安全工具
    text: 文本工具
    utility: 实用工具
    web: 网络工具
  clearSearch: 清除搜索
  colorConverter:
    colorName: 颜色名称
    colorPicker: 颜色选择器
    convert: 转换
    copied: 已复制
    description: 在不同颜色格式之间转换
    format: 格式
    inputColor: 输入颜色
    title: 颜色转换器
    value: 值
  dataProcessing: 数据处理
  description: 实用的在线工具集合
  dialogMode: 弹窗模式
  encryption:
    algorithm: 算法
    algorithms:
      aes: AES
      des: DES
      tripleDesEde: 3DES-EDE
      rsa: RSA
      ecc: ECC
      xor: XOR密码
    asymmetric: 非对称加密
    ciphertext: 密文
    copy: 复制
    decrypt: 解密
    decryptPlaceholder: 输入要解密的文本...
    decryptedResultPlaceholder: 解密结果将显示在此处...
    description: 支持多种算法的高级加密解密工具
    encrypt: 加密
    encryptPlaceholder: 输入要加密的文本...
    encryptedResultPlaceholder: 加密结果将显示在此处...
    encryptionType: 加密类型
    errors:
      decryptionFailed: 解密失败
      encryptionFailed: 加密失败
      inputRequired: 需要输入文本
      invalidKey: 无效的密钥格式
      keyRequired: 需要加密密钥
      privateKeyRequired: 需要私钥
      processingError: 处理过程中发生错误
      publicKeyRequired: 需要公钥
      textRequired: 文本是必需的
    generateIV: 生成IV
    generateKey: 生成密钥
    generateKeyPair: 生成密钥对
    hide: 隐藏
    initializationVector: 初始化向量
    ivPlaceholder: 输入初始化向量（可选）...
    key: 密钥
    keyPlaceholder: 输入加密密钥...
    keySize: 密钥长度
    modes:
      cbc: CBC
      cfb: CFB
      ctr: CTR
      ecb: ECB
      gcm: GCM
      ofb: OFB
    mode: 模式
    padding: 填充
    paddings:
      ansix923: ANSI X9.23
      iso10126: ISO 10126
      pkcs7: PKCS#7
      zero: 零填充
    plaintext: 明文
    privateKey: 私钥
    privateKeyPlaceholder: 输入私钥...
    publicKey: 公钥
    publicKeyPlaceholder: 输入公钥...
    show: 显示
    success:
      copied: 结果已复制到剪贴板
      keyPairGenerated: 密钥对生成成功
    symmetric: 对称加密
    title: 加密解密
    useIv: 使用初始化向量
  history:
    description: 您最近使用的工具
    inputPrefix: "输入:"
    title: 使用历史
  jsonFormatter:
    compactMode: 紧凑模式
    copiedToClipboard: 已复制到剪贴板
    copy: 复制
    description: 格式化、验证和美化 JSON 数据
    download: 下载
    escapeHtml: 转义 HTML
    format: 格式化
    formattedOutputPlaceholder: 格式化后的 JSON 将显示在这里
    indentSpaces: 缩进空格
    inputJson: 输入 JSON
    invalidJsonFormat: 无效的 JSON 格式
    jsonMinify: JSON 压缩
    minify: 压缩
    outputJson: 输出 JSON
    pasteJsonHere: 在此粘贴 JSON 数据...
    pleaseEnterJsonData: 请输入 JSON 数据
    sortKeys: 排序键
    title: JSON 格式化器
    upload: 上传文件
  jwtDecoder:
    decodeButton: 解码
    description: 解码和检查JWT令牌
    errors:
      decodeFailed: 解码失败
      inputRequired: 需要输入内容
      invalidFormat: 无效的JWT格式
    header: 头部
    inputLabel: JWT令牌
    inputPlaceholder: 在此粘贴您的JWT令牌...
    payload: 载荷
    success:
      copied: 已复制到剪贴板
    title: JWT解码器
  nameGenerator:
    count: 数量
    description: 生成来自不同文化的随机姓名
    gender: 性别
    genders:
      female: 女性
      male: 男性
      random: 随机
    generate: 生成
    generatedNames: 生成的姓名
    includeMiddleName: 包含中间名
    nationalities:
      american: 美国
      arabic: 阿拉伯
      british: 英国
      chinese: 中国
      french: 法国
      german: 德国
      indian: 印度
      japanese: 日本
      random: 随机
      russian: 俄国
      spanish: 西班牙
    nationality: 国籍
    noNamesGenerated: 尚未生成姓名
    title: 姓名生成器
    errors:
      generateFailed: 生成姓名失败
  noToolsFound: 未找到工具
  normalMode: 普通模式
  passwordGenerator:
    customCharSet: 自定义字符集
    customCharSetPlaceholder: 输入自定义字符...
    description: 生成具有可自定义选项的安全密码
    errors:
      charSetEmpty: 字符集不能为空
      customCharSetRequired: 使用自定义字符集时，字符集不能为空
      generateFailed: 生成密码失败
    excludeAmbiguous: 排除模糊字符
    excludeSimilar: 排除相似字符
    generatePassword: 生成密码
    includeLowercase: 包含小写字母
    includeNumbers: 包含数字
    includeSpecialChars: 包含特殊字符
    includeUppercase: 包含大写字母
    noPasswordHistory: 暂无密码历史
    passwordHistory: 密码历史
    passwordLength: 密码长度
    passwordStrength: 密码强度
    strength:
      extremelyStrong: 极强
      medium: 中等
      strong: 强
      veryStrong: 非常强
      veryWeak: 非常弱
      weak: 弱
    success:
      copied: 密码已复制到剪贴板
    title: 密码生成器
    useCustomCharSet: 使用自定义字符集
  searchPlaceholder: 搜索工具...
  searchResults: "找到 {{count}} 个工具匹配 '{{query}}'"
  textUtils:
    caseConverter: 大小写转换
    charsLabel: 字符
    copyButton: 复制
    description: 强大的文本处理工具
    encoding: 编码
    errors:
      base64DecodeFailed: Base64解码失败
      base64EncodeFailed: Base64编码失败
      processError: 处理错误
      uriDecodeFailed: URI解码失败
    inputPlaceholder: 输入要处理的文本...
    inputTextLabel: 输入文本
    linesLabel: 行
    operations:
      base64Decode: Base64解码
      base64Encode: Base64编码
      capitalize: 首字母大写
      htmlDecode: HTML解码
      htmlEncode: HTML编码
      lowercase: 小写
      normalizeSpaces: 规范化空格
      removeEmptyLines: 移除空行
      removeSpaces: 移除空格
      reverse: 反转
      reverseSort: 反向排序
      shuffle: 随机排列
      sort: 排序
      spacesToTabs: 空格转制表符
      tabsToSpaces: 制表符转空格
      titleCase: 标题大小写
      trimLines: 修剪行
      uniqueLines: 去重行
      uppercase: 大写
      uriDecode: URI解码
      uriEncode: URI编码
    outputPlaceholder: 处理后的文本将显示在此处...
    outputTextLabel: 输出文本
    processButton: 处理
    textOperations: 文本操作
    title: 文本工具
    whitespace: 空白字符
    wordsLabel: 单词
  title: 在线工具
  tools:
    base64: Base64编码
    base64Desc: Base64 编码和解码
    color: 颜色选择器
    colorDesc: 在不同颜色格式之间转换
    efficientOnlineTools: 高效在线工具集
    efficientOnlineToolsDescription: 精选实用在线工具，提升工作效率，无需安装，即开即用
    encryption: 加密解密
    encryptionDesc: 使用各种算法加密和解密文本
    exploreAllTools: 探索全部工具
    hash: 哈希生成
    hashDesc: 生成文本的哈希值
    json: JSON格式化
    jsonDesc: 格式化、验证和美化 JSON 数据
    jwt: JWT工具
    jwtDesc: 解码和检查 JWT 令牌的内容
    loginToSaveTools: 登录后可保存常用工具
    name: 随机名称
    nameDesc: 生成随机姓名
    onlineTools: 在线工具
    password: 密码生成器
    passwordDesc: 生成安全的随机密码
    saveMyTools: 收藏我的工具
    text: 文本处理
    textDesc: 文本转换和处理工具
    url: URL编码
    urlDesc: URL 编码和解码
    uuid: UUID生成器
    uuidDesc: 生成各种版本的 UUID
    yamlProperties: YAML/Properties转换器
    yamlPropertiesDesc: 在YAML和Properties文件格式之间转换
    yamlPropertiesConverter:
      title: YAML/Properties转换器
      description: 在YAML和Properties文件格式之间转换
      errors:
        inputRequired: 需要输入内容
        conversionFailed: 转换失败
        copyFailed: 复制失败
      success:
        converted: 转换成功
      yamlInput: YAML输入
      propertiesInput: Properties输入
      yamlOutput: YAML输出
      propertiesOutput: Properties输出
      enterYamlContent: 输入YAML内容...
      enterPropertiesContent: 输入Properties内容...
      convertedResultHere: 转换结果将显示在这里...
      copy: 复制
      copied: 已复制
      convert: 转换
      clear: 清除
  toolsCount: 个工具
  toolsList: 工具列表
  tryDifferentSearch: 请尝试不同的搜索关键词
  urlEncoder:
    component: URI 组件
    decode: 解码
    decodeOutput: 解码输出
    decodeOutputPlaceholder: 解码结果将显示在这里...
    description: URL 编码和解码工具
    encode: 编码
    encodeAll: 编码所有字符
    errors:
      decodeFailed: 解码失败
      processError: 处理出错
    inputPlaceholder: 输入要编码的文本...
    inputText: 输入文本
    inputUrl: 输入 URL
    inputUrlPlaceholder: 输入要解码的 URL...
    mode: 模式
    output: 输出
    outputPlaceholder: 编码结果将显示在这里...
    plusForSpace: 空格用 + 替代
    standard: 标准 URI
    title: URL 编码器
  uuid:
    copyAll: 复制全部
    count: 数量
    countLabel: 数量
    description: 生成各种版本的UUID
    errors:
      generateError: 生成错误
      v3Required: v3需要命名空间和名称
      v5Required: v5需要命名空间和名称
    generate: 生成
    generated: 已生成的UUID
    name: 名称
    namePlaceholder: 输入名称...
    namespace: 命名空间
    namespacePlaceholder: 输入命名空间UUID...
    title: UUID生成器
    version: 版本
orPermanent: 或创建永久链接
ourMission: 我们的使命
ourMissionDesc: 构建更好的数字体验
privacyFocus: 隐私保护
privacyFocusDesc: 您的数据安全是我们的优先考虑
profile:
  account: 账户
  accountDescription: 管理您的个人资料信息和头像
  avatarError: 头像操作失败
  avatarRemoved: 头像已移除
  avatarRemovedDesc: 您的头像已成功移除
  avatarUpdated: 头像已更新
  avatarUpdatedDesc: 您的头像已成功更新
  changePicture: 更换头像
  confirmNewPassword: 确认新密码
  confirmNewPasswordPlaceholder: 再次输入新密码
  connect: 连接
  connected: 已连接
  connections: 连接
  currentPassword: 当前密码
  disconnect: 断开连接
  disconnectError: 断开连接失败
  disconnected: 已断开连接
  disconnectedDesc: 第三方账户连接已断开
  emailAddress: 邮箱地址
  emailNotVerified: 邮箱未验证
  emailNotVerifiedAlert: 您的邮箱地址未验证。请检查收件箱并点击验证链接。
  emailSendError: 发送邮件失败
  emailSent: 邮件已发送
  emailVerified: 邮箱已验证
  enterCurrentPassword: 输入当前密码
  enterNewPassword: 输入新密码
  error: 错误
  imageSizeLimit: 图片大小不能超过5MB
  manageConnections: 管理您的账户设置和连接服务
  memberSince: 注册时间
  newPassword: 新密码
  noOAuthProviders: 没有可用的OAuth提供商
  notConnected: 未连接
  password: 密码
  passwordUpdateError: 更新密码失败
  passwordUpdated: 密码已更新
  passwordUpdatedDesc: 您的密码已成功更新
  primary: 主要
  remove: 移除
  selectImage: 选择图片
  selectImageFile: 请选择图片文件
  sendVerificationEmail: 发送验证邮件
  settings: 设置
  subtitle: 管理您的账户设置和偏好
  superAdmin: 超级管理员
  title: 个人资料
  unverified: 未验证
  updatePassword: 更新密码
  uploadImageDesc: 支持JPG、PNG格式，最大5MB
  uploadProfilePicture: 上传头像
  verificationEmailSentDesc: 验证邮件已发送到您的邮箱
  verified: 已验证
quickStart: 快速开始
quickStartDesc: 几次点击即可开始使用我们的工具
receiveMessages: 安全接收消息
registeredUsers: 注册用户
stats:
  clickForDetails: 点击查看详情
  hotValue: 热度值：
  newsCount: 新闻数量：
  platformDistributionAndRankings: 平台分布和热门新闻排行
  todaysHotListAnalytics: 今日热榜分析
  visits: 访问量
status:
  backend: 后端
  checking: 检查中...
  connected: 已连接
  connectionCheckFailed: 连接检查失败
  connectionFailed: 连接失败
  status: 状态
  unknown: 未知
systemSettings: 系统设置
tempEmail: 临时邮箱
tempEmailOptions: 临时邮箱选项
todoMemo:
  addMemo: 添加备忘录
  addTodo: 添加待办
  backToList: 返回列表
  clientPresentation: 客户演示
  completed: 已完成
  contentDisplayError: 内容显示错误
  created: 创建时间
  createdOn: 创建于：
  delete: 删除
  description: 管理允许的域名列表
  dueDate: 截止日期
  dueDates: 截止日期
  dueDatesDesc: 为您的任务设置和管理截止日期
  edit: 编辑
  expand: 展开
  goToLatestMemo: 前往最新备忘录
  high: 高
  imageAlt: "图片{{index}}"
  images: 图片
  latest: 最新
  loading: 加载中...
  login: 登录
  loginRequired: 需要登录
  low: 允许的域名
  medium: 中等
  memo:
    add: 添加分类
    addTag: 添加标签
    clearDate: 清除日期
    clickNewMemoToCreate: 点击"新备忘录"创建您的第一个备忘录
    close: 关闭
    content: 内容
    details: 详情
    editMemo: 编辑备忘录
    edited: 已编辑
    enterMemoContent: 输入备忘录内容
    memos: 备忘录
    messages:
      addFailed: 添加备忘录失败
      added: 备忘录添加成功
      deleteFailed: 删除备忘录失败
      deleted: 备忘录删除成功
      loadFailed: 加载备忘录失败
      loginRequired: 需要登录才能操作备忘录
      updateFailed: 更新备忘录失败
      updated: 备忘录更新成功
    newMemo: 新备忘录
    next: 下一个备忘录
    noMemos: 暂无备忘录
    noMemosOnPage: 此页面没有备忘录
    pageOf: "第{{current}}页，共{{total}}页"
    pickReminderDate: 选择提醒日期
    previous: 上一个备忘录
    reminderDate: 提醒日期
    tags: 标签
    timeAgo:
      dayAgo: 1天前
      daysAgo: "{{count}}天前"
      hourAgo: 1小时前
      hoursAgo: "{{count}}小时前"
      justNow: 刚刚
      minuteAgo: 1分钟前
      minutesAgo: "{{count}}分钟前"
  memoDetails: 备忘录详情
  memos: 备忘录
  minimize: 最小化
  myTasks: 我的任务
  nextMemo: 下一个备忘录
  noActiveTodos: 没有进行中的待办
  noContent: 无内容
  noMemosYet: 暂无备忘录
  pending: 等待审核
  previousMemo: 上一个备忘录
  priority: 优先级
  projectProposal: 项目提案
  quickNote: 快速笔记
  quickNotes: 快速笔记
  quickNotesDesc: 即时捕捉想法和思路
  reminder: 提醒
  reminders: 提醒
  remindersDesc: 获取重要任务和事件的通知
  sampleNote: 这是一个演示备忘录功能的示例笔记
  status: 状态
  tags: 标签
  taskManagement: 任务管理
  taskManagementDesc: 高效组织和跟踪您的任务
  teamMeeting: 团队会议
  title: 待办和备忘录
  todo:
    messages:
      addFailed: 添加待办事项失败
      added: 待办事项添加成功
      deleteFailed: 删除待办事项失败
      deleted: 待办事项删除成功
      loadFailed: 加载待办事项失败
      loginRequired: 需要登录才能操作待办事项
      updateFailed: 更新待办事项失败
      updated: 待办事项更新成功
  todos: 待办
  viewAll: 查看全部
tools:
  colorConverter:
    colorName: 颜色名称
    colorPicker: 取色器
    convert: 转换
    copied: 已复制
    description: 在不同颜色格式之间转换
    format: 格式
    inputColor: 输入颜色
    title: 颜色转换器
    value: 值
  efficientOnlineTools: 高效在线工具集
  efficientOnlineToolsDescription: 精选实用在线工具，提升工作效率，无需安装，即开即用
  exploreAllTools: 探索全部工具
  history:
    description: 最近使用的工具和输入
    inputPrefix: "输入: "
    noHistory: 暂无历史记录
    title: 历史记录
  jsonFormatter:
    compactMode: 紧凑模式
    copiedToClipboard: 已复制到剪贴板
    copy: 复制
    description: 美化或压缩JSON数据以便更好地阅读和分析
    download: 下载
    escapeHtml: 转义HTML
    format: 格式化
    formattedOutputPlaceholder: 格式化输出将显示在此处...
    indentSpaces: 缩进空格
    inputJson: 输入JSON
    invalidJsonFormat: 无效的JSON格式
    jsonMinify: JSON压缩
    minify: 压缩
    outputJson: 输出JSON
    pasteJsonHere: 在此粘贴JSON数据...
    pleaseEnterJsonData: 请输入JSON数据
    sortKeys: 排序键
    title: JSON格式化器
    upload: 上传
  jwtDecoder:
    decodeButton: 解码
    description: 解码和检查JWT令牌
    errors:
      invalidFormat: 无效的JWT格式
    header: 头部
    inputLabel: JWT令牌
    inputPlaceholder: 在此粘贴您的JWT令牌...
    payload: 载荷
    title: JWT解码器
  loginToSaveTools: 登录后可保存常用工具
  nameGenerator:
    count: 数量
    description: 从不同文化中生成随机名称
    gender: 性别
    genders:
      female: 女性
      male: 男性
      random: 随机
    generate: 生成
    generatedNames: 已生成的名称
    includeMiddleName: 包含中间名
    nationalities:
      random: 随机
    nationality: 国籍
    noNamesGenerated: 尚未生成名称
    title: 名称生成器
  onlineTools: 在线工具
  passwordGenerator:
    customCharSet: 自定义字符集
    customCharSetPlaceholder: 输入自定义字符...
    description: 生成具有可自定义选项的安全密码
    excludeAmbiguous: 排除模糊字符
    excludeSimilar: 排除相似字符
    generatePassword: 生成密码
    includeLowercase: 包含小写字母
    includeNumbers: 包含数字
    includeSpecialChars: 包含特殊字符
    includeUppercase: 包含大写字母
    noPasswordHistory: 暂无密码历史
    passwordHistory: 密码历史
    passwordLength: 密码长度
    passwordStrength: 密码强度
    strength:
      extremelyStrong: 极强
      medium: 中等
      strong: 强
      veryStrong: 非常强
      veryWeak: 非常弱
      weak: 弱
    title: 密码生成器
    useCustomCharSet: 使用自定义字符集
  saveMyTools: 收藏我的工具
  textUtils:
    caseConverter: 大小写转换
    charsLabel: 字符
    copyButton: 复制
    description: 强大的文本处理工具
    encoding: 编码
    errors:
      base64DecodeFailed: Base64解码失败
      base64EncodeFailed: Base64编码失败
      processError: 处理错误
      uriDecodeFailed: URI解码失败
    inputPlaceholder: 在此粘贴您的JWT令牌...
    inputTextLabel: 输入文本
    linesLabel: 行
    operations:
      base64Decode: Base64解码
      base64Encode: Base64编码
      capitalize: 首字母大写
      htmlDecode: HTML解码
      htmlEncode: HTML编码
      lowercase: 小写
      normalizeSpaces: 规范化空格
      removeEmptyLines: 移除空行
      removeSpaces: 移除空格
      reverse: 反转
      reverseSort: 反向排序
      shuffle: 随机排列
      sort: 排序
      spacesToTabs: 空格转制表符
      tabsToSpaces: 制表符转空格
      titleCase: 标题大小写
      trimLines: 修剪行
      uniqueLines: 去重行
      uppercase: 大写
      uriDecode: URI解码
      uriEncode: URI编码
    outputPlaceholder: 处理后的文本将显示在此处...
    outputTextLabel: 输出文本
    processButton: 处理
    textOperations: 文本操作
    title: 文本工具
    whitespace: 空白字符
    wordsLabel: 单词
  urlEncoder:
    component: 组件
    decode: 解码
    decodeOutput: 解码输出
    decodeOutputPlaceholder: 解码输出将显示在此处...
    description: 对URL和组件进行编码和解码
    encode: 编码
    encodeAll: 全部编码
    errors:
      decodeFailed: 解码失败
      inputRequired: 需要输入内容
      processError: 处理错误
    inputPlaceholder: 在此粘贴您的JWT令牌...
    inputText: 输入文本
    inputUrl: 输入URL
    inputUrlPlaceholder: 输入要编码/解码的URL...
    mode: 模式
    output: 输出
    outputPlaceholder: 编码输出将显示在此处...
    plusForSpace: 空格用加号
    standard: 标准
    success:
      copied: 已复制到剪贴板
    title: URL编码器/解码器
  uuid:
    copyAll: 复制全部
    count: 数量
    countLabel: 数量
    description: 生成各种版本的UUID
    errors:
      generateError: 生成错误
      v3Required: v3需要命名空间和名称
      v5Required: v5需要命名空间和名称
    generate: 生成
    generated: 已生成的UUID
    name: 名称
    namePlaceholder: 输入名称...
    namespace: 命名空间
    namespacePlaceholder: 输入命名空间UUID...
    title: UUID生成器
    version: 版本
tryItNow: 立即尝试
unregisteredUsers: 未注册用户
upTo30Days: 有效期最长30天
url:
  expiration: 过期时间
  goToHomepage: 返回首页
  invalidLinkDescription: 您要访问的链接不存在或已过期
  invalidShortUrl: 无效的短链接
  never: 永不过期
  oneDay: 1天
  oneHour: 1小时
  oneMonth: 1个月
  oneWeek: 1周
  redirecting: 正在重定向...
  shortUrlNotFound: 短链接未找到
  shortenAUrl: 缩短链接
  shortenUrl: 缩短网址
  shortenUrlDescription: 将长网址转换为短小易分享的链接
  urlExpired: URL已过期
  urlPlaceholder: 输入要缩短的网址
urlExpiration: URL过期选项
urlExpirationDesc: 选择您的短链接保持活跃的时间
urlExpirationOption1: 1小时 - 快速临时分享
urlExpirationOption2: 24小时 - 日常使用链接
urlExpirationOption3: 7天 - 周项目
urlExpirationOption4: 30天 - 月度活动
urlExpirationOption5: 永不过期 - 永久链接
urlFeature1Desc: 将长网址转换为短小易记的链接
urlFeature1Title: 快速URL缩短
urlFeature2Desc: 使用自定义代码创建个性化短链接
urlFeature2Title: 自定义短代码
urlFeature3Desc: 跟踪点击并分析链接性能
urlFeature3Title: 点击分析
urlFeature4Desc: 自动为您的链接生成二维码
urlFeature4Title: 二维码生成
urlFeature5Desc: 一次处理多个URL
urlFeature5Title: 批量URL处理
urlFeature6Desc: 使用我们的API与您的应用程序集成
urlFeature6Title: API访问
urlShortener:
  allowedDomains: 允许的域名
  description: 创建简短易分享的链接
  domains: 域名
  manage: 管理
  more: 更多
  readyToShorten: 准备缩短
  shortenUrl: 缩短网址
  tabs:
    domainWhitelist: 域名白名单
    domains: 域名
    shortenUrls: 缩短网址
    urls: 网址
  title: 网址缩短
userManagement: 用户管理
view: 查看
